# -*- coding: utf-8 -*-
"""
会员等级订单分析AI分析器
提供会员等级订单数据的智能分析功能
"""

import logging
from typing import Dict, Any, List

from services.llm_service import LLMService

logger = logging.getLogger(__name__)

class LevelOrderAiAnalyzer:
    """会员等级订单分析AI分析器"""

    def __init__(self):
        """初始化会员等级订单分析AI分析器"""
        self.llm_service = LLMService()
        logger.info("会员等级订单分析AI分析器初始化完成")

    async def analyze_level_order_data(self, level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        分析会员等级订单数据

        Args:
            level_data: 会员等级订单数据列表
            start_date: 查询开始日期 (YYYY-MM-DD格式)
            end_date: 查询结束日期 (YYYY-MM-DD格式)

        Returns:
            str: AI分析结果（100-150字的整段分析）
        """
        try:
            if not level_data:
                return "会员等级订单数据不足，无法进行有效分析。建议完善数据收集机制，确保各等级会员订单数据的完整性和准确性，为精准营销和会员价值提升提供可靠的数据支撑。"

            # 构建时间范围描述
            time_range_desc = ""
            if start_date and end_date:
                time_range_desc = f"分析时间范围：{start_date} 至 {end_date}"
            elif start_date:
                time_range_desc = f"分析时间范围：{start_date} 开始"
            elif end_date:
                time_range_desc = f"分析时间范围：截至 {end_date}"
            else:
                time_range_desc = "分析时间范围：未指定时间范围"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的会员运营数据分析师，请基于以下会员等级订单数据进行深入分析：

{time_range_desc}

数据概况：
{self._format_level_order_data_summary(level_data, start_date, end_date)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3个关键洞察
2. 每个洞察控制在28字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：峰值表现、结构特征、优化建议
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点28字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                result = analysis_result.strip()
                logger.info(f"会员等级订单数据AI分析完成")
                return result
            else:
                logger.warning("AI分析返回空结果")
                return "会员等级订单数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"会员等级订单数据AI分析失败: {str(e)}")
            return f"会员等级订单数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    def _format_level_order_data_summary(self, level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        格式化会员等级订单数据摘要

        Args:
            level_data: 会员等级订单数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: 格式化的数据摘要
        """
        if not level_data:
            return "无数据"

        try:
            # 提取数据
            level_names = [item.get('ccName', '未知等级') for item in level_data]
            money_ratio_values = [item.get('orderMoneyRatio', 0) for item in level_data]
            num_ratio_values = [item.get('orderNumRatio', 0) for item in level_data]
            repurchase_values = [item.get('repurchaseRate', 0) for item in level_data]

            # 计算统计指标
            total_levels = len(level_data)
            avg_money_ratio = sum(money_ratio_values) / total_levels if total_levels > 0 else 0
            avg_num_ratio = sum(num_ratio_values) / total_levels if total_levels > 0 else 0
            avg_repurchase = sum(repurchase_values) / total_levels if total_levels > 0 else 0

            # 找出极值
            max_money_ratio = max(money_ratio_values) if money_ratio_values else 0
            min_money_ratio = min(money_ratio_values) if money_ratio_values else 0
            max_num_ratio = max(num_ratio_values) if num_ratio_values else 0
            min_num_ratio = min(num_ratio_values) if num_ratio_values else 0
            max_repurchase = max(repurchase_values) if repurchase_values else 0
            min_repurchase = min(repurchase_values) if repurchase_values else 0

            # 找出对应的等级名称
            max_money_ratio_level = level_names[money_ratio_values.index(max_money_ratio)] if money_ratio_values else "未知"
            max_num_ratio_level = level_names[num_ratio_values.index(max_num_ratio)] if num_ratio_values else "未知"
            max_repurchase_level = level_names[repurchase_values.index(max_repurchase)] if repurchase_values else "未知"

            # 构建时间范围信息
            time_info = ""
            if start_date and end_date:
                time_info = f"数据时间范围：{start_date} 至 {end_date}\n"
            elif start_date:
                time_info = f"数据时间范围：{start_date} 开始\n"
            elif end_date:
                time_info = f"数据时间范围：截至 {end_date}\n"

            summary = f"""
{time_info}等级数量：{total_levels}个会员等级
等级名称：{', '.join(level_names)}
消费金额占比：平均{avg_money_ratio:.2f}%，最高{max_money_ratio:.2f}%({max_money_ratio_level})，最低{min_money_ratio:.2f}%
消费订单占比：平均{avg_num_ratio:.2f}%，最高{max_num_ratio:.2f}%({max_num_ratio_level})，最低{min_num_ratio:.2f}%
复购率：平均{avg_repurchase:.2f}%，最高{max_repurchase:.2f}%({max_repurchase_level})，最低{min_repurchase:.2f}%
等级差异：消费金额占比差距{max_money_ratio - min_money_ratio:.2f}%，订单占比差距{max_num_ratio - min_num_ratio:.2f}%，复购率差距{max_repurchase - min_repurchase:.2f}%
详细数据：{[f"{level_names[i]}(金额{money_ratio_values[i]:.2f}%/订单{num_ratio_values[i]:.2f}%/复购{repurchase_values[i]:.2f}%)" for i in range(len(level_names))]}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化会员等级订单数据摘要失败: {str(e)}")
            return f"数据格式化失败: {str(e)}"

    def _get_fallback_analysis(self, level_data: List[Dict[str, Any]]) -> str:
        """
        获取备用分析结果

        Args:
            level_data: 会员等级订单数据列表

        Returns:
            str: 备用分析结果
        """
        if not level_data:
            return "会员等级订单数据不足，无法进行有效分析。建议完善数据收集机制，确保各等级会员订单数据的完整性和准确性。"

        try:
            # 简单的数据分析
            level_count = len(level_data)
            money_ratios = [item.get('orderMoneyRatio', 0) for item in level_data]
            repurchase_rates = [item.get('repurchaseRate', 0) for item in level_data]

            max_money_ratio = max(money_ratios) if money_ratios else 0
            max_repurchase = max(repurchase_rates) if repurchase_rates else 0

            return f"当前共有{level_count}个会员等级，消费金额占比最高达{max_money_ratio:.1f}%，复购率最高为{max_repurchase:.1f}%。建议针对高价值等级会员制定专属营销策略，通过个性化服务和精准推荐提升复购率，同时优化低等级会员的消费体验，促进等级提升和消费增长。"

        except Exception as e:
            logger.error(f"生成备用分析失败: {str(e)}")
            return "会员等级订单数据分析遇到技术问题，建议检查数据完整性后重新分析。"


# 创建全局分析器实例
level_order_ai_analyzer = LevelOrderAiAnalyzer()