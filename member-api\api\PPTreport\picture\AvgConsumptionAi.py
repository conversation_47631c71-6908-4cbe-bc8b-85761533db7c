# -*- coding: utf-8 -*-
"""
会员平均消费数据AI分析器
提供会员现金消费笔数、储值消费笔数、单均消费、人均贡献数据的智能分析功能
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional

from services.llm_service import LLMService

logger = logging.getLogger(__name__)

class AvgConsumptionAiAnalyzer:
    """会员平均消费数据AI分析器"""

    def __init__(self):
        """初始化会员平均消费数据AI分析器"""
        self.llm_service = LLMService()
        logger.info("会员平均消费数据AI分析器初始化完成")

    async def analyze_avg_consumption_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员平均消费数据

        Args:
            monthly_data: 去年月度平均消费数据列表

        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年会员平均消费数据分析：数据不足，无法进行有效分析。"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的数据分析师，请基于以下去年会员平均消费数据进行深入分析：

数据概况：
{self._format_avg_consumption_data_summary(monthly_data)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3个关键洞察
2. 每个洞察控制在28字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：峰值表现、结构特征、优化建议
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点28字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("去年会员平均消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "去年会员平均消费数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"去年会员平均消费数据AI分析失败: {str(e)}")
            return f"去年会员平均消费数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def analyze_avg_consumption_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员平均消费数据（包含对比分析）

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年会员平均消费数据分析：数据不足，无法进行有效分析。"

            # 构建完整的AI分析请求
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_avg_consumption_data_summary(last_year_data[:len(this_year_data)])}
"""

            full_prompt = f"""
作为专业的数据分析师，请基于以下会员平均消费数据进行深入的环比和同比动态分析：

今年数据概况：
{self._format_avg_consumption_data_summary(this_year_data)}
{comparison_section}

分析要求：
1. 使用"1、2、3、4、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在38字以内（标点符号不计入字数），突出关键数据和核心趋势
3. 重点关注：同比增长率、最新月份表现、关键指标变化、渠道效果对比
4. 必须标注具体月份、数值和变化百分比
5. 每个洞察格式：关键发现 + 核心数据 + 趋势判断
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号
8. 进行深入的动态分析：分析月度环比变化趋势、季节性规律、消费增长或下降的原因

请基于实际数据直接生成专业对比洞察（每点38字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("今年会员平均消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "今年会员平均消费数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"今年会员平均消费数据AI分析失败: {str(e)}")
            return f"今年会员平均消费数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def generate_all_avg_consumption_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员平均消费相关的AI分析

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年月度平均消费数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        try:
            logger.info("开始生成会员平均消费AI分析...")

            # 并行执行两个分析任务
            tasks = [
                self.analyze_avg_consumption_last_year_data(last_year_data),
                self.analyze_avg_consumption_this_year_data(this_year_data, last_year_data)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            last_year_analysis = results[0] if not isinstance(results[0], Exception) else "去年平均消费数据分析失败"
            this_year_analysis = results[1] if not isinstance(results[1], Exception) else "今年平均消费数据分析失败"

            analysis_results = {
                "avg_consumption_last_year_analysis_report": last_year_analysis,
                "avg_consumption_this_year_analysis_report": this_year_analysis
            }

            logger.info("会员平均消费AI分析生成完成")
            return analysis_results

        except Exception as e:
            logger.error(f"生成会员平均消费AI分析失败: {str(e)}")
            return {
                "avg_consumption_last_year_analysis_report": "去年会员平均消费数据分析失败，请检查数据源和分析逻辑。",
                "avg_consumption_this_year_analysis_report": "今年会员平均消费数据分析失败，请检查数据源和分析逻辑。"
            }

    def _format_avg_consumption_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化会员平均消费数据摘要

        Args:
            monthly_data: 月度平均消费数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"

        try:
            # 计算关键指标
            cash_consume_counts = [item.get('cash_consume_count', 0) for item in monthly_data]
            prepay_consume_counts = [item.get('prepay_consume_count', 0) for item in monthly_data]
            avg_consume_amounts = [item.get('avg_consume_amount', 0) for item in monthly_data]
            avg_contributions = [item.get('avg_contribution', 0) for item in monthly_data]

            avg_cash_consume_count = sum(cash_consume_counts) / len(cash_consume_counts) if cash_consume_counts else 0
            avg_prepay_consume_count = sum(prepay_consume_counts) / len(prepay_consume_counts) if prepay_consume_counts else 0
            avg_consume_amount = sum(avg_consume_amounts) / len(avg_consume_amounts) if avg_consume_amounts else 0
            avg_contribution = sum(avg_contributions) / len(avg_contributions) if avg_contributions else 0

            total_cash_consume_count = sum(cash_consume_counts)
            total_prepay_consume_count = sum(prepay_consume_counts)

            # 计算储值消费占比
            total_count = total_cash_consume_count + total_prepay_consume_count
            prepay_ratio = (total_prepay_consume_count / total_count * 100) if total_count > 0 else 0

            summary = f"""
数据期间：{len(monthly_data)}个月
现金消费笔数：月均{avg_cash_consume_count:.0f}笔，累计{total_cash_consume_count:.0f}笔
储值消费笔数：月均{avg_prepay_consume_count:.0f}笔，累计{total_prepay_consume_count:.0f}笔，占比{prepay_ratio:.1f}%
会员单均消费：月均{avg_consume_amount:.2f}元
会员人均贡献：月均{avg_contribution:.2f}元
月度数据：{[f"{item.get('month', 'N/A')}月(现金{item.get('cash_consume_count', 0):.0f}笔/储值{item.get('prepay_consume_count', 0):.0f}笔/单均{item.get('avg_consume_amount', 0):.2f}元/人均{item.get('avg_contribution', 0):.2f}元)" for item in monthly_data]}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化会员平均消费数据摘要失败: {str(e)}")
            return f"数据格式化失败: {str(e)}"


# 创建全局分析器实例
avg_consumption_ai_analyzer = AvgConsumptionAiAnalyzer()