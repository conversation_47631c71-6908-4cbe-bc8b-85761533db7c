# -*- coding: utf-8 -*-
"""
会员消费数量分析AI模块
专门处理会员消费数量数据的AI分析功能
"""

import logging
from typing import Dict, Any, List, Optional
import asyncio

from services.llm_service import LLMService

logger = logging.getLogger(__name__)

class ConsumptionNumAiAnalyzer:
    """会员消费数量AI分析器"""

    def __init__(self):
        """初始化会员消费数量AI分析器"""
        self.llm_service = LLMService()
        logger.info("会员消费数量AI分析器初始化完成")

    async def analyze_consumption_num_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员消费数量数据

        Args:
            monthly_data: 去年月度消费数量数据列表

        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年会员消费数量数据分析：数据不足，无法进行有效分析。"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的数据分析师，请基于以下去年会员消费数量数据进行深入分析：

数据概况：
{self._format_consumption_num_data_summary(monthly_data)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3个关键洞察
2. 每个洞察控制在28字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：峰值表现、结构特征、优化建议
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点28字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("去年会员消费数量数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "去年会员消费数量数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"去年会员消费数量数据AI分析失败: {str(e)}")
            return f"去年会员消费数量数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def analyze_consumption_num_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员消费数量数据（包含对比分析）

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年会员消费数量数据分析：数据不足，无法进行有效分析。"

            # 构建完整的AI分析请求
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_consumption_num_data_summary(last_year_data[:len(this_year_data)])}
"""

            full_prompt = f"""
作为专业的数据分析师，请基于以下会员消费数量数据进行深入的环比和同比动态分析：

今年数据概况：
{self._format_consumption_num_data_summary(this_year_data)}
{comparison_section}

分析要求：
1. 使用"1、2、3、4、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在38字以内（标点符号不计入字数），突出关键数据和核心趋势
3. 重点关注：同比增长率、最新月份表现、关键指标变化、渠道效果对比
4. 必须标注具体月份、数值和变化百分比
5. 每个洞察格式：关键发现 + 核心数据 + 趋势判断
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号
8. 进行深入的动态分析：分析月度环比变化趋势、季节性规律、消费增长或下降的原因

请基于实际数据直接生成专业对比洞察（每点38字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("今年会员消费数量数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "今年会员消费数量数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"今年会员消费数量数据AI分析失败: {str(e)}")
            return f"今年会员消费数量数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    def _format_consumption_num_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化月度消费数量数据摘要

        Args:
            monthly_data: 月度消费数量数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"

        summary_lines = []
        total_consume_users_sum = 0
        total_consume_count_sum = 0
        total_charge_count_sum = 0

        for item in monthly_data:
            month = item.get('month', '未知月份')
            consume_users = item.get('consume_users', 0)
            total_consume_count = item.get('total_consume_count', 0)
            charge_count = item.get('charge_count', 0)
            consume_frequency = item.get('consume_frequency', 0)

            total_consume_users_sum += consume_users
            total_consume_count_sum += total_consume_count
            total_charge_count_sum += charge_count

            summary_lines.append(
                f"{month}: 消费人数{consume_users}人, 消费笔数{total_consume_count}笔, "
                f"充值笔数{charge_count}笔, 消费频次{consume_frequency:.2f}次/人"
            )

        # 添加汇总信息
        avg_consume_users = total_consume_users_sum / len(monthly_data) if monthly_data else 0
        avg_total_consume = total_consume_count_sum / len(monthly_data) if monthly_data else 0
        avg_charge_count = total_charge_count_sum / len(monthly_data) if monthly_data else 0
        avg_frequency = (total_consume_count_sum / total_consume_users_sum) if total_consume_users_sum > 0 else 0

        summary_lines.append(f"\n汇总: 月均消费人数{avg_consume_users:.0f}人, 月均消费笔数{avg_total_consume:.0f}笔, 月均充值笔数{avg_charge_count:.0f}笔, 平均消费频次{avg_frequency:.2f}次/人")

        return "\n".join(summary_lines)

    async def generate_all_consumption_num_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员消费数量相关的AI分析

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年月度消费数量数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        try:
            logger.info("开始生成会员消费数量AI分析...")

            # 并行执行两个分析任务
            tasks = [
                self.analyze_consumption_num_last_year_data(last_year_data),
                self.analyze_consumption_num_this_year_data(this_year_data, last_year_data)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            last_year_analysis = results[0] if not isinstance(results[0], Exception) else "去年消费数量数据分析失败"
            this_year_analysis = results[1] if not isinstance(results[1], Exception) else "今年消费数量数据分析失败"

            analysis_results = {
                "consumption_num_last_year_analysis_report": last_year_analysis,
                "consumption_num_this_year_analysis_report": this_year_analysis
            }

            logger.info("会员消费数量AI分析生成完成")
            return analysis_results

        except Exception as e:
            logger.error(f"生成会员消费数量AI分析失败: {str(e)}")
            return {
                "consumption_num_last_year_analysis_report": "去年会员消费数量数据分析生成失败",
                "consumption_num_this_year_analysis_report": "今年会员消费数量数据分析生成失败"
            }


# 创建全局会员消费数量AI分析器实例
consumption_num_ai_analyzer = ConsumptionNumAiAnalyzer()