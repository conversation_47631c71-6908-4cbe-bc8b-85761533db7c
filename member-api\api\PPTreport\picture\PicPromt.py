# -*- coding: utf-8 -*-
"""
图片AI分析提示词模板
针对图片数据分析提供专业的AI分析模板
"""

from typing import Dict, Any, List

class PictureAnalysisPrompts:
    """图片AI分析提示词类"""

    # 新增会员数据分析模板句式（去年数据）
    NEW_MEMBER_LAST_YEAR_TEMPLATES = [
        "去年全年新增会员数据显示，月均新增{avg_new_members:.0f}人，峰值出现在{peak_month}（{peak_value}人），低谷在{valley_month}（{valley_value}人），{trend_analysis}。",
        "去年取关数据表现为月均{avg_unfollow:.0f}人，取关率平均{avg_unfollow_rate:.2f}%，{unfollow_trend_analysis}，{retention_assessment}。",
        "去年会员增长呈现{growth_pattern}特征，{seasonal_analysis}，{growth_stability_analysis}。",
        "去年数据基础表明{baseline_analysis}，为今年发展{baseline_significance}。",
        "去年会员质量{quality_analysis}，取关控制{unfollow_control_analysis}。"
    ]

    # 新增会员数据分析模板句式（今年数据，包含对比）
    NEW_MEMBER_THIS_YEAR_TEMPLATES = [
        "今年截至目前新增会员{total_new_members}人，月均{avg_new_members:.0f}人，较去年同期{comparison_trend}，{performance_analysis}。",
        "今年取关数据显示月均{avg_unfollow:.0f}人，取关率{avg_unfollow_rate:.2f}%，相比去年{unfollow_comparison}，{retention_improvement}。",
        "今年会员增长{growth_trend}，{monthly_performance}，{growth_quality_analysis}。",
        "对比去年同期，今年{year_over_year_analysis}，{competitive_position}。",
        "今年会员发展{development_assessment}，{future_projection}。"
    ]

    # 数据趋势分析词汇
    TREND_ANALYSIS_TERMS = {
        "positive": ["呈现稳步上升趋势", "表现出良好增长态势", "显示积极发展势头", "展现强劲增长动力"],
        "negative": ["出现下降趋势", "表现相对疲软", "增长动力不足", "面临增长挑战"],
        "stable": ["保持相对稳定", "波动较小", "增长平稳", "发展稳健"],
        "volatile": ["波动较大", "起伏明显", "变化频繁", "不够稳定"]
    }

    # 季节性分析词汇
    SEASONAL_ANALYSIS_TERMS = [
        "春节期间增长放缓，3-5月回升明显",
        "夏季表现活跃，秋季增长稳定",
        "年末冲刺效果显著，节假日影响明显",
        "淡旺季差异较大，需要针对性策略"
    ]

    # 对比分析词汇
    COMPARISON_TERMS = {
        "better": ["表现更优", "有所改善", "明显提升", "显著改进"],
        "worse": ["有所下降", "表现不如", "需要改进", "存在差距"],
        "similar": ["基本持平", "相差不大", "保持稳定", "变化不明显"]
    }

    @staticmethod
    def get_new_member_last_year_analysis_prompt(monthly_data: List[Dict[str, Any]]) -> str:
        """
        生成去年新增会员数据分析的按点格式提示词（洞见性+实操性）

        Args:
            monthly_data: 去年月度数据列表

        Returns:
            str: 按点格式的分析提示词
        """
        if not monthly_data:
            return "• 数据不足：去年新增会员数据缺失，建议完善数据收集机制"

        # 计算统计数据
        new_members = [item['new_members'] for item in monthly_data]
        unfollow_members = [item['new_unfollow_members'] for item in monthly_data]
        unfollow_rates = [item['unfollow_rate'] for item in monthly_data]

        avg_new_members = sum(new_members) / len(new_members) if new_members else 0
        avg_unfollow = sum(unfollow_members) / len(unfollow_members) if unfollow_members else 0
        avg_unfollow_rate = sum(unfollow_rates) / len(unfollow_rates) if unfollow_rates else 0
        total_new = sum(new_members)

        # 检查是否有企微数据
        wechat_enabled = monthly_data[0].get('wechat_enabled', False) if monthly_data else False
        wechat_friends = [item.get('wechat_new_friends', 0) for item in monthly_data] if wechat_enabled else []
        avg_wechat_friends = sum(wechat_friends) / len(wechat_friends) if wechat_friends else 0
        total_wechat_friends = sum(wechat_friends) if wechat_friends else 0

        # 找出峰值和低谷
        max_idx = new_members.index(max(new_members))
        min_idx = new_members.index(min(new_members))
        peak_month = monthly_data[max_idx]['month']
        valley_month = monthly_data[min_idx]['month']
        peak_value = new_members[max_idx]
        valley_value = new_members[min_idx]

        # 计算波动性
        variance = sum((x - avg_new_members) ** 2 for x in new_members) / len(new_members) if new_members else 0
        volatility = (variance ** 0.5) / avg_new_members * 100 if avg_new_members > 0 else 0

        # 生成按点分析
        analysis_points = []

        # 1. 增长规模洞察（20字以内）
        if avg_new_members > 1200:
            analysis_points.append(f"• 月均新增{avg_new_members:.0f}人表现优异，建议扩大投入")
        elif avg_new_members > 800:
            analysis_points.append(f"• 月均新增{avg_new_members:.0f}人稳健，需优化获客策略")
        else:
            analysis_points.append(f"• 月均新增{avg_new_members:.0f}人偏低，需重新评估渠道")

        # 2. 季节性波动洞察（20字以内）
        if volatility > 30:
            analysis_points.append(f"• {peak_month}峰值{peak_value}人，{valley_month}低谷{valley_value}人，波动较大")
        else:
            analysis_points.append(f"• 月度增长稳定，建议寻找新增长点")

        # 3. 留存质量洞察（20字以内）
        if avg_unfollow_rate < 5:
            analysis_points.append(f"• 取关率{avg_unfollow_rate:.1f}%优秀，建议标准化流程")
        elif avg_unfollow_rate < 8:
            analysis_points.append(f"• 取关率{avg_unfollow_rate:.1f}%良好，需优化引导流程")
        else:
            analysis_points.append(f"• 取关率{avg_unfollow_rate:.1f}%偏高，需分析流失原因")

        # 4. 企微渠道或基础价值洞察（20字以内）
        net_growth_rate = (total_new - sum(unfollow_members)) / total_new * 100 if total_new > 0 else 0
        if wechat_enabled and total_wechat_friends > 0:
            # 有企微数据的情况
            wechat_conversion_rate = total_wechat_friends / total_new * 100 if total_new > 0 else 0
            if wechat_conversion_rate > 30:
                analysis_points.append(f"• 企微转化{wechat_conversion_rate:.1f}%优异，建议重点投入")
            elif wechat_conversion_rate > 15:
                analysis_points.append(f"• 企微转化{wechat_conversion_rate:.1f}%良好，需优化策略")
            else:
                analysis_points.append(f"• 企微转化{wechat_conversion_rate:.1f}%偏低，需加强推广")
        else:
            # 无企微数据的情况
            if net_growth_rate > 90:
                analysis_points.append(f"• 净增长率{net_growth_rate:.1f}%优异，建议扩大规模")
            else:
                analysis_points.append(f"• 净增长率{net_growth_rate:.1f}%一般，需提升质量")

        return "\n".join(analysis_points)

    @staticmethod
    def get_new_member_this_year_analysis_prompt(
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> str:
        """
        生成今年新增会员数据分析的按点格式提示词（包含对比分析，洞见性+实操性）

        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: 按点格式的对比分析提示词
        """
        if not this_year_data:
            return "• 数据不足：今年新增会员数据缺失，建议立即启动数据收集和分析机制"

        # 今年数据统计
        this_new_members = [item['new_members'] for item in this_year_data]
        this_unfollow_members = [item['new_unfollow_members'] for item in this_year_data]
        this_unfollow_rates = [item['unfollow_rate'] for item in this_year_data]

        total_new_members = sum(this_new_members)
        avg_new_members = total_new_members / len(this_new_members) if this_new_members else 0
        avg_unfollow_rate = sum(this_unfollow_rates) / len(this_unfollow_rates) if this_unfollow_rates else 0

        # 检查是否有企微数据
        wechat_enabled = this_year_data[0].get('wechat_enabled', False) if this_year_data else False
        this_wechat_friends = [item.get('wechat_new_friends', 0) for item in this_year_data] if wechat_enabled else []
        total_wechat_friends = sum(this_wechat_friends) if this_wechat_friends else 0
        avg_wechat_friends = total_wechat_friends / len(this_wechat_friends) if this_wechat_friends else 0

        analysis_points = []

        # 对比分析（如果有去年数据）
        if last_year_data and len(last_year_data) >= len(this_year_data):
            # 取去年同期数据进行对比
            last_same_period = last_year_data[:len(this_year_data)]
            last_new_members = [item['new_members'] for item in last_same_period]
            last_unfollow_rates = [item['unfollow_rate'] for item in last_same_period]

            last_avg_new = sum(last_new_members) / len(last_new_members) if last_new_members else 0
            last_avg_unfollow_rate = sum(last_unfollow_rates) / len(last_unfollow_rates) if last_unfollow_rates else 0
            last_total = sum(last_new_members)

            # 企微数据对比（如果今年有企微数据）
            last_wechat_friends = []
            last_total_wechat = 0
            if wechat_enabled:
                last_wechat_friends = [item.get('wechat_new_friends', 0) for item in last_same_period]
                last_total_wechat = sum(last_wechat_friends)

            # 1. 增长对比洞察（20字以内）
            new_member_change = (avg_new_members - last_avg_new) / last_avg_new * 100 if last_avg_new > 0 else 0
            total_change = (total_new_members - last_total) / last_total * 100 if last_total > 0 else 0

            if new_member_change > 15:
                analysis_points.append(f"• 同比增长{new_member_change:.1f}%表现卓越，建议扩大投入")
            elif new_member_change > 5:
                analysis_points.append(f"• 同比增长{new_member_change:.1f}%稳步提升，需持续优化")
            elif new_member_change > -5:
                analysis_points.append(f"• 同比变化{new_member_change:.1f}%基本持平，需调整策略")
            else:
                analysis_points.append(f"• 同比下降{abs(new_member_change):.1f}%，需紧急制定恢复计划")

            # 2. 留存对比洞察（20字以内）
            unfollow_change = avg_unfollow_rate - last_avg_unfollow_rate
            if unfollow_change < -1:
                analysis_points.append(f"• 取关率降低{abs(unfollow_change):.1f}%至{avg_unfollow_rate:.1f}%，留存改善")
            elif unfollow_change < 0.5:
                analysis_points.append(f"• 取关率{avg_unfollow_rate:.1f}%与去年持平，需优化体验")
            else:
                analysis_points.append(f"• 取关率上升{unfollow_change:.1f}%至{avg_unfollow_rate:.1f}%，需改进")

            # 3. 企微渠道对比洞察（20字以内）
            if wechat_enabled and total_wechat_friends > 0:
                wechat_change = total_wechat_friends - last_total_wechat if last_total_wechat > 0 else total_wechat_friends
                wechat_conversion_rate = total_wechat_friends / total_new_members * 100 if total_new_members > 0 else 0

                if wechat_change > 0 and last_total_wechat > 0:
                    wechat_growth_rate = wechat_change / last_total_wechat * 100
                    analysis_points.append(f"• 企微同比增长{wechat_growth_rate:.1f}%，建议加大投入")
                elif total_wechat_friends > 0:
                    analysis_points.append(f"• 企微新增{total_wechat_friends}人占比{wechat_conversion_rate:.1f}%，需优化策略")
                else:
                    analysis_points.append(f"• 企微获客效果有限，需重新评估策略")

            # 4. 综合效率洞察（20字以内）
            this_net_rate = (total_new_members - sum(this_unfollow_members)) / total_new_members * 100 if total_new_members > 0 else 0
            last_net_rate = (last_total - sum(item.get('new_unfollow_members', 0) for item in last_same_period)) / last_total * 100 if last_total > 0 else 0
            net_efficiency_change = this_net_rate - last_net_rate

            if net_efficiency_change > 2:
                analysis_points.append(f"• 净增长效率提升{net_efficiency_change:.1f}%，建议标准化")
            elif net_efficiency_change > -1:
                analysis_points.append(f"• 净增长效率持平，需寻找新突破点")
            else:
                analysis_points.append(f"• 净增长效率下降{abs(net_efficiency_change):.1f}%，需改进")

        else:
            # 无对比数据时的独立分析（20字以内）
            if avg_new_members > 1200:
                analysis_points.append(f"• 月均新增{avg_new_members:.0f}人表现优异，建议规模化")
            elif avg_new_members > 800:
                analysis_points.append(f"• 月均新增{avg_new_members:.0f}人稳健，需优化渠道")
            else:
                analysis_points.append(f"• 月均新增{avg_new_members:.0f}人偏低，需重新制定策略")

            if avg_unfollow_rate < 5:
                analysis_points.append(f"• 取关率{avg_unfollow_rate:.1f}%优秀，建议标准化流程")
            else:
                analysis_points.append(f"• 取关率{avg_unfollow_rate:.1f}%偏高，需加强引导")

            # 企微渠道独立分析（20字以内）
            if wechat_enabled and total_wechat_friends > 0:
                wechat_conversion_rate = total_wechat_friends / total_new_members * 100 if total_new_members > 0 else 0
                if wechat_conversion_rate > 25:
                    analysis_points.append(f"• 企微转化{wechat_conversion_rate:.1f}%优异，建议重点发展")
                elif wechat_conversion_rate > 10:
                    analysis_points.append(f"• 企微转化{wechat_conversion_rate:.1f}%良好，需优化流程")
                else:
                    analysis_points.append(f"• 企微转化{wechat_conversion_rate:.1f}%偏低，需加强推广")

        # 4. 发展趋势洞察（20字以内）
        if len(this_year_data) >= 3:
            recent_trend = sum(this_new_members[-2:]) / 2 - sum(this_new_members[:2]) / 2
            if recent_trend > 50:
                analysis_points.append(f"• 近期增长加速明显，建议加大投入")
            elif recent_trend > -50:
                analysis_points.append(f"• 增长保持稳定，需监控市场变化")
            else:
                analysis_points.append(f"• 近期增长放缓，需分析原因改进")

        return "\n".join(analysis_points)

    # 会员消费数据分析模板句式（去年数据）
    MEMBER_CONSUMPTION_LAST_YEAR_TEMPLATES = [
        "去年全年消费数据显示，月均总实收{avg_total_amount:.2f}万元，储值消费月均{avg_prepay_amount:.2f}万元，现金消费月均{avg_cash_amount:.2f}万元，{payment_structure_analysis}。",
        "去年储值消费表现为月均{avg_prepay_amount:.2f}万元，{prepay_trend_analysis}，{prepay_assessment}。",
        "去年现金消费呈现{cash_pattern}特征，月均{avg_cash_amount:.2f}万元，{cash_seasonal_analysis}，{cash_stability_analysis}。",
        "去年消费结构{structure_analysis}，支付习惯{payment_habit_analysis}，为今年发展{structure_significance}。",
        "去年消费质量{quality_analysis}，双色柱状图显示储值与现金消费{consumption_balance_analysis}。"
    ]

    # 会员消费数据分析模板句式（今年数据）
    MEMBER_CONSUMPTION_THIS_YEAR_TEMPLATES = [
        "今年消费数据显示，月均总实收{avg_total_amount:.2f}万元，同比{total_growth_trend}，储值消费{avg_prepay_amount:.2f}万元，现金消费{avg_cash_amount:.2f}万元，{comparison_analysis}。",
        "今年储值消费{prepay_comparison}，月均{avg_prepay_amount:.2f}万元，{prepay_strategy_analysis}。",
        "今年现金消费{cash_comparison}，{cash_growth_analysis}，{cash_optimization_suggestion}。",
        "今年双色柱状图显示{structure_comparison}，{structure_optimization}，{future_trend_prediction}。"
    ]

    # 消费结构分析词汇
    CONSUMPTION_STRUCTURE_TERMS = [
        "储值消费为主导，现金消费为补充",
        "现金消费占主导，储值消费需提升",
        "储值与现金消费相对均衡",
        "支付结构需要优化调整"
    ]

    # 消费趋势分析词汇
    CONSUMPTION_TREND_TERMS = [
        "呈现稳步增长态势",
        "波动较大需要关注",
        "增长乏力需要改进",
        "表现优异值得推广"
    ]

    @staticmethod
    def get_member_consumption_last_year_analysis_prompt(monthly_data: List[Dict[str, Any]]) -> str:
        """
        生成去年会员消费数据分析的按点格式提示词（洞见性+实操性）

        Args:
            monthly_data: 去年月度消费数据列表

        Returns:
            str: 按点格式的分析提示词
        """
        if not monthly_data:
            return "• 数据不足：去年会员消费数据缺失，建议完善数据收集机制"

        # 计算关键指标
        total_amounts = [item.get('total_actual_amount', 0) for item in monthly_data]
        prepay_amounts = [item.get('prepay_actual_amount', 0) for item in monthly_data]
        cash_amounts = [item.get('actual_amount', 0) for item in monthly_data]

        # 基础统计
        avg_total_amount = sum(total_amounts) / len(total_amounts) if total_amounts else 0
        avg_prepay_amount = sum(prepay_amounts) / len(prepay_amounts) if prepay_amounts else 0
        avg_cash_amount = sum(cash_amounts) / len(cash_amounts) if cash_amounts else 0

        # 找出峰值和低谷
        max_total_idx = total_amounts.index(max(total_amounts))
        min_total_idx = total_amounts.index(min(total_amounts))
        peak_month = monthly_data[max_total_idx]['month']
        valley_month = monthly_data[min_total_idx]['month']
        peak_value = max(total_amounts)
        valley_value = min(total_amounts)

        # 计算波动性
        variance = sum((x - avg_total_amount) ** 2 for x in total_amounts) / len(total_amounts) if total_amounts else 0
        volatility = (variance ** 0.5) / avg_total_amount * 100 if avg_total_amount > 0 else 0

        # 生成按点分析
        analysis_points = []

        # 1. 消费规模洞察 + 实操建议
        if avg_total_amount > 500:
            analysis_points.append(f"• 消费规模优异：月均总实收{avg_total_amount:.2f}万元，全年累计{sum(total_amounts):.2f}万元，建议复制成功经验并扩大会员消费促进活动")
        elif avg_total_amount > 200:
            analysis_points.append(f"• 消费规模稳健：月均总实收{avg_total_amount:.2f}万元，有提升空间，建议优化消费激励策略并加大营销投入")
        else:
            analysis_points.append(f"• 消费规模偏低：月均总实收{avg_total_amount:.2f}万元，需重点关注，建议重新评估消费促进策略并制定增长计划")

        # 2. 支付结构洞察 + 实操建议（基于双色柱状图）
        if avg_prepay_amount > avg_cash_amount * 1.5:
            analysis_points.append(f"• 储值消费主导：储值消费{avg_prepay_amount:.2f}万元，现金消费{avg_cash_amount:.2f}万元，建议平衡支付结构并提升现金消费转化")
        elif avg_prepay_amount > avg_cash_amount * 0.7:
            analysis_points.append(f"• 支付结构均衡：储值消费{avg_prepay_amount:.2f}万元，现金消费{avg_cash_amount:.2f}万元，建议保持现有结构并优化储值充值激励")
        else:
            analysis_points.append(f"• 现金消费主导：储值消费{avg_prepay_amount:.2f}万元，现金消费{avg_cash_amount:.2f}万元，建议加强储值推广并提升会员粘性")

        # 3. 季节性波动洞察 + 实操建议
        if volatility > 30:
            analysis_points.append(f"• 波动性较大：峰值{peak_month}({peak_value:.2f}万元)与低谷{valley_month}({valley_value:.2f}万元)差异明显，建议制定淡旺季差异化营销策略")
        else:
            analysis_points.append(f"• 消费稳定：月度波动控制良好，建议保持现有运营策略并寻找新的消费增长点")

        # 4. 消费质量评估 + 实操建议
        total_sum = sum(total_amounts)
        if total_sum > 3000:
            analysis_points.append(f"• 消费质量优秀：全年总实收{total_sum:.2f}万元，消费基础扎实，建议建立会员分层管理体系并推出高价值服务")
        else:
            analysis_points.append(f"• 消费潜力待挖：全年总实收{total_sum:.2f}万元，建议加强会员运营并提升客单价和消费频次")

        return "\n".join(analysis_points)

    @staticmethod
    def get_member_consumption_this_year_analysis_prompt(
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> str:
        """
        生成今年会员消费数据分析的按点格式提示词（对比分析+实操性）

        Args:
            this_year_data: 今年月度消费数据列表
            last_year_data: 去年同期数据列表

        Returns:
            str: 按点格式的对比分析提示词
        """
        if not this_year_data:
            return "• 数据不足：今年会员消费数据缺失，建议立即启动数据收集和分析机制"

        # 今年数据统计
        this_total_amounts = [item.get('total_actual_amount', 0) for item in this_year_data]
        this_prepay_amounts = [item.get('prepay_actual_amount', 0) for item in this_year_data]
        this_cash_amounts = [item.get('actual_amount', 0) for item in this_year_data]

        this_avg_total = sum(this_total_amounts) / len(this_total_amounts) if this_total_amounts else 0
        this_avg_prepay = sum(this_prepay_amounts) / len(this_prepay_amounts) if this_prepay_amounts else 0
        this_avg_cash = sum(this_cash_amounts) / len(this_cash_amounts) if this_cash_amounts else 0

        # 去年同期数据统计（如果有）
        growth_total = 0
        growth_prepay = 0
        growth_cash = 0

        if last_year_data:
            # 取去年同期数据（相同月份数量）
            last_year_same_period = last_year_data[:len(this_year_data)]
            last_total_amounts = [item.get('total_actual_amount', 0) for item in last_year_same_period]
            last_prepay_amounts = [item.get('prepay_actual_amount', 0) for item in last_year_same_period]
            last_cash_amounts = [item.get('actual_amount', 0) for item in last_year_same_period]

            last_avg_total = sum(last_total_amounts) / len(last_total_amounts) if last_total_amounts else 0
            last_avg_prepay = sum(last_prepay_amounts) / len(last_prepay_amounts) if last_prepay_amounts else 0
            last_avg_cash = sum(last_cash_amounts) / len(last_cash_amounts) if last_cash_amounts else 0

            # 计算增长率
            if last_avg_total > 0:
                growth_total = ((this_avg_total - last_avg_total) / last_avg_total) * 100
            if last_avg_prepay > 0:
                growth_prepay = ((this_avg_prepay - last_avg_prepay) / last_avg_prepay) * 100
            if last_avg_cash > 0:
                growth_cash = ((this_avg_cash - last_avg_cash) / last_avg_cash) * 100

        # 生成按点分析
        analysis_points = []

        # 1. 消费增长对比洞察 + 实操建议
        if last_year_data:
            if growth_total > 20:
                analysis_points.append(f"• 消费增长强劲：月均总实收{this_avg_total:.2f}万元，同比增长{growth_total:.2f}%，建议加大成功策略投入并设定更高增长目标")
            elif growth_total > 0:
                analysis_points.append(f"• 消费稳步增长：月均总实收{this_avg_total:.2f}万元，同比增长{growth_total:.2f}%，建议分析增长驱动因素并持续优化")
            else:
                analysis_points.append(f"• 消费增长承压：月均总实收{this_avg_total:.2f}万元，同比下降{abs(growth_total):.2f}%，建议紧急制定消费提升计划")
        else:
            analysis_points.append(f"• 消费基础建立：月均总实收{this_avg_total:.2f}万元，建议建立完整的消费数据监控体系")

        # 2. 支付结构变化洞察 + 实操建议（基于双色柱状图）
        if last_year_data:
            prepay_change_rate = ((this_avg_prepay - last_avg_prepay) / last_avg_prepay * 100) if last_avg_prepay > 0 else 0
            cash_change_rate = ((this_avg_cash - last_avg_cash) / last_avg_cash * 100) if last_avg_cash > 0 else 0

            if prepay_change_rate > cash_change_rate + 10:
                analysis_points.append(f"• 储值消费增长突出：储值消费{this_avg_prepay:.2f}万元，同比增长{prepay_change_rate:.2f}%，建议优化储值服务体验并推出更多储值权益")
            elif cash_change_rate > prepay_change_rate + 10:
                analysis_points.append(f"• 现金消费增长突出：现金消费{this_avg_cash:.2f}万元，同比增长{cash_change_rate:.2f}%，建议加强储值推广并分析现金消费增长原因")
            else:
                analysis_points.append(f"• 支付结构均衡发展：储值消费{this_avg_prepay:.2f}万元，现金消费{this_avg_cash:.2f}万元，建议保持现有策略并寻找新的优化点")
        else:
            analysis_points.append(f"• 支付结构初建：储值消费{this_avg_prepay:.2f}万元，现金消费{this_avg_cash:.2f}万元，建议建立支付结构优化目标")

        # 3. 运营效率对比洞察 + 实操建议
        if last_year_data and growth_total != 0:
            efficiency_score = (growth_prepay + growth_cash) / 2
            if efficiency_score > growth_total:
                analysis_points.append(f"• 运营效率提升：各项消费指标增长均衡，储值增长{growth_prepay:.2f}%，现金增长{growth_cash:.2f}%，建议总结成功经验并推广")
            else:
                analysis_points.append(f"• 运营效率待优：部分消费指标增长不均，建议重点关注薄弱环节并制定针对性改进措施")
        else:
            recent_months = this_total_amounts[-3:] if len(this_total_amounts) >= 3 else this_total_amounts
            recent_avg = sum(recent_months) / len(recent_months)
            if recent_avg > this_avg_total:
                analysis_points.append(f"• 运营效率向好：近期消费表现优于平均水平，建议保持当前运营策略并适度扩大投入")
            else:
                analysis_points.append(f"• 运营效率需关注：近期消费表现有所波动，建议分析运营策略有效性并及时调整")

        # 4. 发展趋势预测 + 实操建议
        if len(this_total_amounts) >= 3:
            recent_trend = ((this_total_amounts[-1] - this_total_amounts[-3]) / this_total_amounts[-3]) * 100 if this_total_amounts[-3] and this_total_amounts[-3] > 0 else 0
            if recent_trend > 15:
                analysis_points.append(f"• 发展趋势向好：近期消费增长加速，建议抓住机遇扩大营销投入并制定更积极的发展目标")
            elif recent_trend > -15:
                analysis_points.append(f"• 发展趋势平稳：消费保持稳定增长态势，建议持续监控市场变化并适时调整策略")
            else:
                analysis_points.append(f"• 发展趋势放缓：近期消费增长有所下降，建议及时分析原因并采取针对性改进措施")

        return "\n".join(analysis_points)