# -*- coding: utf-8 -*-
"""
图片AI分析模块
专门处理图片数据的AI分析功能
"""

import logging
from typing import Dict, Any, List, Optional
import asyncio

from services.llm_service import LLMService
from .AvgConsumptionAi import avg_consumption_ai_analyzer
from .ConsumptionNumAi import consumption_num_ai_analyzer
from .MemberChargeAi import member_charge_ai_analyzer
from .LevelConsumptionAi import level_consumption_ai_analyzer
from .LevelOrderAi import level_order_ai_analyzer
from .LevelNumberAi import level_number_ai_analyzer
from .ChargeDistribution.ChargeDistributionAi import charge_distribution_ai_analyzer

logger = logging.getLogger(__name__)

class PictureAiAnalyzer:
    """图片AI分析器"""

    def __init__(self):
        """初始化图片AI分析器"""
        self.llm_service = LLMService()
        logger.info("图片AI分析器初始化完成")

    async def analyze_new_member_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年新增会员数据

        Args:
            monthly_data: 去年月度数据列表

        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                logger.warning("去年新增会员数据为空")
                return "去年新增会员数据分析：数据不足，无法进行有效分析。"

            # 检查数据质量
            valid_data_count = sum(1 for item in monthly_data if item.get('new_members') is not None and item.get('month'))
            logger.info(f"去年数据质量检查：总数据{len(monthly_data)}条，有效数据{valid_data_count}条")

            if valid_data_count == 0:
                logger.warning("去年新增会员数据无有效记录")
                return "去年新增会员数据分析：数据质量不足，所有记录都缺少关键字段，无法进行分析。"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的数据分析师，请基于以下去年新增会员数据进行深入分析：

数据概况：
{self._format_monthly_data_summary(monthly_data)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3个关键洞察
2. 每个洞察控制在28字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：峰值表现、结构特征、优化建议
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点28字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("去年新增会员数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "去年新增会员数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"去年新增会员数据AI分析失败: {str(e)}")
            logger.error(f"数据样本: {monthly_data[:2] if monthly_data else '无数据'}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return f"去年新增会员数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def analyze_new_member_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年新增会员数据（包含对比分析）

        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                logger.warning("今年新增会员数据为空")
                return "今年新增会员数据分析：数据不足，无法进行有效分析。"

            # 检查数据质量
            valid_data_count = sum(1 for item in this_year_data if item.get('new_members') is not None and item.get('month'))
            logger.info(f"今年数据质量检查：总数据{len(this_year_data)}条，有效数据{valid_data_count}条")

            if valid_data_count == 0:
                logger.warning("今年新增会员数据无有效记录")
                return "今年新增会员数据分析：数据质量不足，所有记录都缺少关键字段，无法进行分析。"

            # 构建完整的AI分析请求
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_monthly_data_summary(last_year_data[:len(this_year_data)])}
"""

            full_prompt = f"""
作为专业的数据分析师，请基于以下新增会员数据进行深入的环比和同比动态分析：

今年数据概况：
{self._format_monthly_data_summary(this_year_data)}
{comparison_section}

分析要求：
1. 使用"1、2、3、4、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在38字以内（标点符号不计入字数），突出关键数据和核心趋势
3. 重点关注：同比增长率、最新月份表现、关键指标变化、渠道效果对比
4. 必须标注具体月份、数值和变化百分比
5. 每个洞察格式：关键发现 + 核心数据 + 趋势判断
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号
8. 进行深入的动态分析：分析月度环比变化趋势、季节性规律、增长加速或放缓的原因

请基于实际数据直接生成专业对比洞察（每点38字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("今年新增会员数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "今年新增会员数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"今年新增会员数据AI分析失败: {str(e)}")
            logger.error(f"今年数据样本: {this_year_data[:2] if this_year_data else '无数据'}")
            logger.error(f"去年数据样本: {last_year_data[:2] if last_year_data else '无数据'}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return f"今年新增会员数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    def _format_monthly_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化月度数据摘要

        Args:
            monthly_data: 月度数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"

        try:
            # 安全地计算汇总统计，使用.get()方法避免KeyError
            total_new = sum(item.get('new_members', 0) for item in monthly_data if item.get('new_members') is not None)
            total_unfollow = sum(item.get('new_unfollow_members', 0) for item in monthly_data if item.get('new_unfollow_members') is not None)

            # 计算平均取关率，处理除零和None值
            valid_unfollow_rates = [item.get('unfollow_rate', 0) for item in monthly_data if item.get('unfollow_rate') is not None]
            avg_unfollow_rate = sum(valid_unfollow_rates) / len(valid_unfollow_rates) if valid_unfollow_rates and len(valid_unfollow_rates) > 0 else 0

            # 检查是否有企微数据
            wechat_enabled = monthly_data[0].get('wechat_enabled', False) if monthly_data else False
            wechat_summary = ""
            if wechat_enabled:
                total_wechat = sum(item.get('wechat_new_friends', 0) for item in monthly_data)
                wechat_summary = f"\n- 总企微加好友：{total_wechat:,}人"

            # 安全地找出最高和最低月份
            valid_months = [item for item in monthly_data if item.get('new_members') is not None]
            if valid_months:
                max_month = max(valid_months, key=lambda x: x.get('new_members', 0))
                min_month = min(valid_months, key=lambda x: x.get('new_members', 0))
                max_month_info = f"- 最高月份：{max_month.get('month', '未知')}（{max_month.get('new_members', 0):,}人）"
                min_month_info = f"- 最低月份：{min_month.get('month', '未知')}（{min_month.get('new_members', 0):,}人）"
            else:
                max_month_info = "- 最高月份：数据缺失"
                min_month_info = "- 最低月份：数据缺失"

            # 添加数据质量说明
            data_quality_note = ""
            if len(valid_months) < len(monthly_data):
                missing_count = len(monthly_data) - len(valid_months)
                data_quality_note = f"\n- 数据说明：{missing_count}个月的数据不完整"

            summary = f"""
- 总新增会员：{total_new:,}人
- 总取关会员：{total_unfollow:,}人
- 平均取关率：{avg_unfollow_rate:.2f}%{wechat_summary}
{max_month_info}
{min_month_info}
- 数据月份：{len(monthly_data)}个月{data_quality_note}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化月度数据摘要失败: {str(e)}")
            logger.error(f"数据样本: {monthly_data[:2] if monthly_data else '无数据'}")  # 记录前两条数据用于调试
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return f"数据包含{len(monthly_data)}个月的记录，但格式化时遇到问题：{str(e)}"

    async def generate_all_new_member_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有新增会员相关的AI分析

        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年月度数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        try:
            logger.info("开始生成新增会员AI分析...")

            # 并行执行两个分析任务
            tasks = [
                self.analyze_new_member_last_year_data(last_year_data),
                self.analyze_new_member_this_year_data(this_year_data, last_year_data)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果，记录详细的异常信息
            if isinstance(results[0], Exception):
                logger.error(f"去年数据分析异常: {str(results[0])}")
                last_year_analysis = f"去年数据分析失败：{str(results[0])}"
            else:
                last_year_analysis = results[0]

            if isinstance(results[1], Exception):
                logger.error(f"今年数据分析异常: {str(results[1])}")
                this_year_analysis = f"今年数据分析失败：{str(results[1])}"
            else:
                this_year_analysis = results[1]

            analysis_results = {
                "new_member_add_last_year_analysis_report": last_year_analysis,
                "new_member_add_this_year_analysis_report": this_year_analysis
            }

            logger.info("新增会员AI分析生成完成")
            return analysis_results

        except Exception as e:
            logger.error(f"生成新增会员AI分析失败: {str(e)}")
            logger.error(f"今年数据样本: {this_year_data[:2] if this_year_data else '无数据'}")
            logger.error(f"去年数据样本: {last_year_data[:2] if last_year_data else '无数据'}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return {
                "new_member_add_last_year_analysis_report": f"去年新增会员数据分析生成失败：{str(e)}",
                "new_member_add_this_year_analysis_report": f"今年新增会员数据分析生成失败：{str(e)}"
            }

    async def analyze_member_consumption_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员消费数据

        Args:
            monthly_data: 去年月度消费数据列表

        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年会员消费数据分析：数据不足，无法进行有效分析。"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的数据分析师，请基于以下去年会员消费数据进行深入分析：

数据概况：
{self._format_consumption_data_summary(monthly_data)}

分析要求：
1. 使用"1、2、3、"作为项目符号，按点列出3个关键洞察
2. 每个洞察控制在28字以内（标点符号不计入字数），突出关键发现和实用建议
3. 重点关注：峰值表现、结构特征、优化建议
4. 必须标注具体数值和关键指标
5. 每个洞察格式：关键发现 + 核心数据 + 简要建议
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号

请基于实际数据直接生成专业洞察（每点28字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("去年会员消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "去年会员消费数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"去年会员消费数据AI分析失败: {str(e)}")
            return f"去年会员消费数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def analyze_member_consumption_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]] = None
    ) -> str:
        """
        分析今年会员消费数据（包含对比分析）

        Args:
            this_year_data: 今年月度消费数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年会员消费数据分析：数据不足，无法进行有效分析。"

            # 构建完整的AI分析请求
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_consumption_data_summary(last_year_data[:len(this_year_data)])}
"""

            full_prompt = f"""
作为专业的数据分析师，请基于以下会员消费数据进行深入的环比和同比动态分析：

今年数据概况：
{self._format_consumption_data_summary(this_year_data)}
{comparison_section}

分析要求：
1. 使用"1、2、3、4、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在38字以内（标点符号不计入字数），突出关键数据和核心趋势
3. 重点关注：同比增长率、最新月份表现、关键指标变化、渠道效果对比
4. 必须标注具体月份、数值和变化百分比
5. 每个洞察格式：关键发现 + 核心数据 + 趋势判断
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号
8. 进行深入的动态分析：分析月度环比变化趋势、季节性规律、消费增长或下降的原因

请基于实际数据直接生成专业对比洞察（每点38字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("今年会员消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果")
                return "今年会员消费数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"今年会员消费数据AI分析失败: {str(e)}")
            return f"今年会员消费数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    async def generate_all_member_consumption_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员消费相关的AI分析

        Args:
            this_year_data: 今年月度消费数据列表
            last_year_data: 去年月度消费数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        try:
            logger.info("开始生成会员消费AI分析...")

            # 并行执行两个分析任务
            tasks = [
                self.analyze_member_consumption_last_year_data(last_year_data),
                self.analyze_member_consumption_this_year_data(this_year_data, last_year_data)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            last_year_analysis = results[0] if not isinstance(results[0], Exception) else "去年消费数据分析失败"
            this_year_analysis = results[1] if not isinstance(results[1], Exception) else "今年消费数据分析失败"

            analysis_results = {
                "member_consumption_last_year_analysis_report": last_year_analysis,
                "member_consumption_this_year_analysis_report": this_year_analysis
            }

            logger.info("会员消费AI分析生成完成")
            return analysis_results

        except Exception as e:
            logger.error(f"生成会员消费AI分析失败: {str(e)}")
            return {
                "member_consumption_last_year_analysis_report": "去年会员消费数据分析生成失败",
                "member_consumption_this_year_analysis_report": "今年会员消费数据分析生成失败"
            }

    def _format_consumption_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化月度消费数据摘要（双色柱状图版本）

        Args:
            monthly_data: 月度消费数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"

        summary_lines = []
        total_amount_sum = 0
        prepay_amount_sum = 0
        cash_amount_sum = 0

        for item in monthly_data:
            month = item.get('month', '未知月份')
            total_amount = item.get('total_actual_amount', 0)
            prepay_amount = item.get('prepay_actual_amount', 0)
            cash_amount = item.get('actual_amount', 0)

            total_amount_sum += total_amount
            prepay_amount_sum += prepay_amount
            cash_amount_sum += cash_amount

            summary_lines.append(
                f"{month}: 总实收{total_amount:.2f}万元, 储值消费{prepay_amount:.2f}万元, "
                f"现金消费{cash_amount:.2f}万元"
            )

        # 添加汇总信息（基于双色柱状图数据）
        avg_total = total_amount_sum / len(monthly_data) if monthly_data else 0
        avg_prepay = prepay_amount_sum / len(monthly_data) if monthly_data else 0
        avg_cash = cash_amount_sum / len(monthly_data) if monthly_data else 0

        summary_lines.append(f"\n汇总: 月均总实收{avg_total:.2f}万元, 月均储值消费{avg_prepay:.2f}万元, 月均现金消费{avg_cash:.2f}万元")

        return "\n".join(summary_lines)

    async def analyze_avg_consumption_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员平均消费数据

        Args:
            monthly_data: 去年月度平均消费数据列表

        Returns:
            str: AI分析结果
        """
        return await avg_consumption_ai_analyzer.analyze_avg_consumption_last_year_data(monthly_data)

    async def analyze_avg_consumption_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员平均消费数据（包含对比分析）

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        return await avg_consumption_ai_analyzer.analyze_avg_consumption_this_year_data(this_year_data, last_year_data)

    async def generate_all_avg_consumption_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员平均消费相关的AI分析

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年月度平均消费数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        return await avg_consumption_ai_analyzer.generate_all_avg_consumption_analysis(this_year_data, last_year_data)

    async def analyze_consumption_num_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员消费数量数据

        Args:
            monthly_data: 去年月度消费数量数据列表

        Returns:
            str: AI分析结果
        """
        return await consumption_num_ai_analyzer.analyze_consumption_num_last_year_data(monthly_data)

    async def analyze_consumption_num_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员消费数量数据（包含对比分析）

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        return await consumption_num_ai_analyzer.analyze_consumption_num_this_year_data(this_year_data, last_year_data)

    async def generate_all_consumption_num_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员消费数量相关的AI分析

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年月度消费数量数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        return await consumption_num_ai_analyzer.generate_all_consumption_num_analysis(this_year_data, last_year_data)

    async def analyze_member_charge_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员充值消费数据

        Args:
            monthly_data: 去年月度充值消费数据列表

        Returns:
            str: AI分析结果
        """
        return await member_charge_ai_analyzer.analyze_member_charge_last_year_data(monthly_data)

    async def analyze_member_charge_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员充值消费数据（包含对比分析）

        Args:
            this_year_data: 今年月度充值消费数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        return await member_charge_ai_analyzer.analyze_member_charge_this_year_data(this_year_data, last_year_data)

    async def generate_all_member_charge_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员充值消费相关的AI分析

        Args:
            this_year_data: 今年月度充值消费数据列表
            last_year_data: 去年月度充值消费数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        return await member_charge_ai_analyzer.generate_all_member_charge_analysis(this_year_data, last_year_data)

    async def analyze_level_consumption_data(self, level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        分析会员等级消费数据

        Args:
            level_data: 会员等级消费数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: AI分析结果
        """
        return await level_consumption_ai_analyzer.analyze_level_consumption_data(level_data, start_date, end_date)

    async def generate_level_consumption_analysis(self, level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> Dict[str, str]:
        """
        生成会员等级消费分析报告

        Args:
            level_data: 会员等级消费数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            Dict: 包含分析结果的字典
        """
        try:
            logger.info("开始生成会员等级消费AI分析...")

            analysis_result = await self.analyze_level_consumption_data(level_data, start_date, end_date)

            result = {
                "level_consumption_analysis_report": analysis_result
            }

            logger.info("会员等级消费AI分析生成完成")
            return result

        except Exception as e:
            logger.error(f"生成会员等级消费AI分析失败: {str(e)}")
            return {
                "level_consumption_analysis_report": "会员等级消费数据分析失败，请检查数据源和分析逻辑。"
            }

    async def analyze_level_order_data(self, level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        分析会员等级订单数据

        Args:
            level_data: 会员等级订单数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: AI分析结果
        """
        return await level_order_ai_analyzer.analyze_level_order_data(level_data, start_date, end_date)

    async def generate_level_order_analysis(self, level_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> Dict[str, str]:
        """
        生成会员等级订单分析报告

        Args:
            level_data: 会员等级订单数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            Dict: 包含分析结果的字典
        """
        try:
            logger.info("开始生成会员等级订单AI分析...")

            analysis_result = await self.analyze_level_order_data(level_data, start_date, end_date)

            result = {
                "level_order_analysis_report": analysis_result
            }

            logger.info("会员等级订单AI分析生成完成")
            return result

        except Exception as e:
            logger.error(f"生成会员等级订单AI分析失败: {str(e)}")
            return {
                "level_order_analysis_report": "会员等级订单数据分析失败，请检查数据源和分析逻辑。"
            }

    async def analyze_level_number_data(self, level_data: List[Dict[str, Any]], history_date: str = None, final_date: str = None) -> str:
        """
        分析会员等级人数数据

        Args:
            level_data: 会员等级人数数据列表
            history_date: 历史日期
            final_date: 期末日期

        Returns:
            str: AI分析结果
        """
        return await level_number_ai_analyzer.analyze_level_number_data(level_data, history_date, final_date)

    async def generate_level_number_analysis(self, level_data: List[Dict[str, Any]], history_date: str = None, final_date: str = None) -> Dict[str, str]:
        """
        生成会员等级人数分析报告

        Args:
            level_data: 会员等级人数数据列表
            history_date: 历史日期
            final_date: 期末日期

        Returns:
            Dict: 包含分析结果的字典
        """
        try:
            logger.info("开始生成会员等级人数AI分析...")

            analysis_result = await self.analyze_level_number_data(level_data, history_date, final_date)

            result = {
                "level_number_analysis_report": analysis_result
            }

            logger.info("会员等级人数AI分析生成完成")
            return result

        except Exception as e:
            logger.error(f"生成会员等级人数AI分析失败: {str(e)}")
            return {
                "level_number_analysis_report": "会员等级人数数据分析失败，请检查数据源和分析逻辑。"
            }

    async def generate_charge_distribution_analysis(self, charge_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> Dict[str, str]:
        """
        生成充值档位分布AI分析

        Args:
            charge_data: 充值档位分布数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            Dict: 包含AI分析结果的字典
        """
        try:
            logger.info(f"开始生成充值档位分布AI分析，数据量: {len(charge_data) if charge_data else 0}")

            # 调用专门的充值档位分布AI分析器
            analysis_result = await charge_distribution_ai_analyzer.analyze_charge_distribution_data(
                charge_data, start_date, end_date
            )

            result = {
                "charge_distribution_analysis_report": analysis_result
            }

            logger.info("充值档位分布AI分析生成完成")
            return result

        except Exception as e:
            logger.error(f"生成充值档位分布AI分析失败: {str(e)}")
            return {
                "charge_distribution_analysis_report": "充值档位分布数据分析失败，请检查数据源和分析逻辑。"
            }