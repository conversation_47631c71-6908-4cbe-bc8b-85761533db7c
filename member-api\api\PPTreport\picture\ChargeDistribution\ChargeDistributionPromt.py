# -*- coding: utf-8 -*-
"""
充值档位分布分析AI分析提示词模板
提供充值档位分布数据的智能分析提示词生成功能
"""

from typing import Dict, Any, List
import random

class ChargeDistributionAnalysisPrompts:
    """充值档位分布分析AI分析提示词类"""

    @staticmethod
    def get_charge_distribution_analysis_prompt(charge_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        生成充值档位分布分析提示词

        Args:
            charge_data: 充值档位分布数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: 分析提示词
        """
        if not charge_data:
            return "充值档位分布数据不足，无法进行有效分析。建议完善数据收集机制，确保充值档位数据的完整性和准确性。"

        try:
            # 提取关键数据
            charge_amounts = [item.get('charge_cash', 0) for item in charge_data]
            charge_counts = [item.get('charge_count', 0) for item in charge_data]

            # 计算基础统计
            total_tiers = len(charge_data)
            total_transactions = sum(charge_counts)
            avg_amount = sum(charge_amounts) / total_tiers if total_tiers > 0 else 0
            avg_count = sum(charge_counts) / total_tiers if total_tiers > 0 else 0

            # 找出关键档位
            max_count_idx = charge_counts.index(max(charge_counts)) if charge_counts else 0
            min_count_idx = charge_counts.index(min(charge_counts)) if charge_counts else 0
            max_amount_idx = charge_amounts.index(max(charge_amounts)) if charge_amounts else 0
            min_amount_idx = charge_amounts.index(min(charge_amounts)) if charge_amounts else 0

            popular_tier = charge_amounts[max_count_idx]
            unpopular_tier = charge_amounts[min_count_idx]
            highest_tier = charge_amounts[max_amount_idx]
            lowest_tier = charge_amounts[min_amount_idx]

            popular_count = charge_counts[max_count_idx]
            unpopular_count = charge_counts[min_count_idx]

            # 计算集中度
            sorted_counts = sorted(charge_counts, reverse=True)
            top3_count = sum(sorted_counts[:3])
            concentration_ratio = (top3_count / total_transactions * 100) if total_transactions > 0 else 0

            # 分析档位分布特征
            distribution_insights = []

            # 1. 档位集中度分析
            if concentration_ratio > 70:
                distribution_insights.append(f"充值行为高度集中，前3档位占{concentration_ratio:.1f}%，用户偏好明确")
            elif concentration_ratio > 50:
                distribution_insights.append(f"充值行为相对集中，前3档位占{concentration_ratio:.1f}%，存在主流偏好")
            else:
                distribution_insights.append(f"充值行为较为分散，前3档位仅占{concentration_ratio:.1f}%，用户需求多样化")

            # 2. 热门档位分析
            popular_ratio = (popular_count / total_transactions * 100) if total_transactions > 0 else 0
            if popular_ratio > 30:
                distribution_insights.append(f"{popular_tier:.0f}元档位极受欢迎，占{popular_ratio:.1f}%，应重点维护")
            elif popular_ratio > 15:
                distribution_insights.append(f"{popular_tier:.0f}元档位较受欢迎，占{popular_ratio:.1f}%，可适度推广")
            else:
                distribution_insights.append(f"最热门档位{popular_tier:.0f}元仅占{popular_ratio:.1f}%，需优化档位设置")

            # 3. 档位范围分析
            tier_range = highest_tier - lowest_tier
            if tier_range > 1000:
                distribution_insights.append(f"档位跨度大({lowest_tier:.0f}-{highest_tier:.0f}元)，覆盖多层次需求")
            elif tier_range > 500:
                distribution_insights.append(f"档位跨度适中({lowest_tier:.0f}-{highest_tier:.0f}元)，满足主要需求")
            else:
                distribution_insights.append(f"档位跨度较小({lowest_tier:.0f}-{highest_tier:.0f}元)，可考虑扩展范围")

            # 4. 运营建议
            operation_suggestions = []
            
            if popular_ratio > 25:
                operation_suggestions.append(f"针对热门{popular_tier:.0f}元档位推出充值优惠活动")
            
            if concentration_ratio < 60:
                operation_suggestions.append("优化档位设置，引导用户向主流档位集中")
            
            if total_tiers > 8:
                operation_suggestions.append("简化档位选择，减少用户决策成本")
            
            if unpopular_count < total_transactions * 0.05:
                operation_suggestions.append(f"考虑取消{unpopular_tier:.0f}元等冷门档位")

            # 随机选择分析角度
            analysis_templates = [
                f"充值档位分布呈现{random.choice(['集中型', '分散型', '均衡型'])}特征，{random.choice(distribution_insights)}。{random.choice(operation_suggestions)}，同时{random.choice(['加强用户引导', '优化充值体验', '完善激励机制'])}，提升充值转化效率。",
                
                f"从{total_tiers}个档位的分布看，{random.choice(distribution_insights)}。{random.choice(operation_suggestions)}，建议{random.choice(['重点推广主流档位', '优化档位结构', '加强精准营销'])}，提升用户充值意愿和频次。",
                
                f"充值行为分析显示{random.choice(distribution_insights)}。{random.choice(operation_suggestions)}，通过{random.choice(['数据驱动优化', '用户行为引导', '个性化推荐'])}，实现充值收入的持续增长。"
            ]

            return random.choice(analysis_templates)

        except Exception as e:
            return f"充值档位分布分析生成失败: {str(e)}"

    @staticmethod
    def get_fallback_analysis() -> str:
        """
        获取备用分析文本

        Returns:
            str: 备用分析文本
        """
        fallback_options = [
            "充值档位分布数据显示用户充值行为存在一定规律性，建议根据主流档位优化充值策略，通过精准营销和个性化推荐提升用户充值体验，同时关注冷门档位的优化调整，实现充值收入的稳定增长。",
            
            "从充值档位分布情况来看，用户充值偏好相对集中，建议重点维护热门档位的优势地位，适度调整档位设置以满足不同用户需求，通过数据驱动的运营策略优化充值转化率，提升整体充值效果。",
            
            "充值档位分布反映了用户的消费能力和充值习惯，建议基于数据洞察优化档位结构，加强对主流档位的推广力度，同时通过用户行为分析和精准营销，引导用户向高价值档位转化，实现充值收入最大化。"
        ]
        
        return random.choice(fallback_options)

    @staticmethod
    def get_detailed_analysis_prompt(charge_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        生成详细的充值档位分布分析提示词

        Args:
            charge_data: 充值档位分布数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: 详细分析提示词
        """
        if not charge_data:
            return ChargeDistributionAnalysisPrompts.get_fallback_analysis()

        try:
            # 提取数据
            charge_amounts = [item.get('charge_cash', 0) for item in charge_data]
            charge_counts = [item.get('charge_count', 0) for item in charge_data]
            total_transactions = sum(charge_counts)

            # 计算详细统计
            sorted_data = sorted(zip(charge_amounts, charge_counts), key=lambda x: x[1], reverse=True)
            top3_data = sorted_data[:3]
            
            # 构建详细分析
            analysis_parts = []
            
            # 时间范围
            if start_date and end_date:
                analysis_parts.append(f"在{start_date}至{end_date}期间")
            
            # 总体概况
            analysis_parts.append(f"共有{len(charge_data)}个充值档位，总计{total_transactions}笔充值")
            
            # 热门档位分析
            if top3_data:
                top_tier = top3_data[0]
                top_ratio = (top_tier[1] / total_transactions * 100) if total_transactions > 0 else 0
                analysis_parts.append(f"其中{top_tier[0]:.0f}元档位最受欢迎，占比{top_ratio:.1f}%")
            
            # 集中度分析
            top3_count = sum([item[1] for item in top3_data])
            concentration = (top3_count / total_transactions * 100) if total_transactions > 0 else 0
            analysis_parts.append(f"前三档位集中度为{concentration:.1f}%")
            
            # 运营建议
            if concentration > 60:
                analysis_parts.append("建议重点维护主流档位优势，适度推广次热门档位")
            else:
                analysis_parts.append("建议优化档位设置，引导用户向主流档位集中")
            
            return "，".join(analysis_parts) + "。"

        except Exception as e:
            return ChargeDistributionAnalysisPrompts.get_fallback_analysis()

    @staticmethod
    def get_charge_frequency_analysis_prompt(frequency_data: List[Dict[str, Any]], start_date: str = None, end_date: str = None) -> str:
        """
        生成充值频次分布分析提示词

        Args:
            frequency_data: 充值频次分布数据列表
            start_date: 查询开始日期
            end_date: 查询结束日期

        Returns:
            str: 分析提示词
        """
        if not frequency_data:
            return "充值频次分布数据不足，无法进行有效分析。建议完善数据收集机制，确保充值频次数据的完整性和准确性。"

        try:
            # 提取关键数据
            frequency_ranges = [item.get('charge_count_range', '') for item in frequency_data]
            user_counts = [item.get('user_count', 0) for item in frequency_data]

            # 计算基础统计
            total_users = sum(user_counts)

            # 找出关键频次
            max_count_idx = user_counts.index(max(user_counts)) if user_counts else 0
            most_active_frequency = frequency_ranges[max_count_idx]
            most_active_count = user_counts[max_count_idx]

            # 计算单次充值用户占比
            single_charge_count = 0
            for i, freq in enumerate(frequency_ranges):
                if freq == "1次":
                    single_charge_count = user_counts[i]
                    break

            single_charge_ratio = (single_charge_count / total_users * 100) if total_users > 0 else 0

            # 计算高频用户占比（3次以上）
            high_frequency_count = 0
            for i, freq in enumerate(frequency_ranges):
                if freq in ["3次", "4次", "5次", "5次以上"]:
                    high_frequency_count += user_counts[i]

            high_frequency_ratio = (high_frequency_count / total_users * 100) if total_users > 0 else 0

            # 分析充值频次特征
            frequency_insights = []

            # 1. 用户忠诚度分析
            if single_charge_ratio > 60:
                frequency_insights.append(f"单次充值用户占{single_charge_ratio:.1f}%，用户粘性较低，需加强留存策略")
            elif single_charge_ratio > 40:
                frequency_insights.append(f"单次充值用户占{single_charge_ratio:.1f}%，用户粘性中等，有提升空间")
            else:
                frequency_insights.append(f"单次充值用户仅占{single_charge_ratio:.1f}%，用户粘性良好")

            # 2. 高频用户分析
            if high_frequency_ratio > 20:
                frequency_insights.append(f"高频用户占{high_frequency_ratio:.1f}%，忠诚用户基础扎实")
            elif high_frequency_ratio > 10:
                frequency_insights.append(f"高频用户占{high_frequency_ratio:.1f}%，核心用户群体稳定")
            else:
                frequency_insights.append(f"高频用户仅占{high_frequency_ratio:.1f}%，需培养忠诚用户")

            # 3. 主要频次分析
            most_active_ratio = (most_active_count / total_users * 100) if total_users > 0 else 0
            if most_active_frequency == "1次":
                frequency_insights.append(f"用户以单次充值为主，占{most_active_ratio:.1f}%，需引导复购")
            elif most_active_frequency in ["2次", "3次"]:
                frequency_insights.append(f"{most_active_frequency}充值用户最多，占{most_active_ratio:.1f}%，显示良好的复购意愿")
            else:
                frequency_insights.append(f"{most_active_frequency}充值用户最多，占{most_active_ratio:.1f}%，用户忠诚度较高")

            # 4. 运营建议
            operation_suggestions = []

            if single_charge_ratio > 50:
                operation_suggestions.append("针对单次充值用户推出复购激励活动")

            if high_frequency_ratio < 15:
                operation_suggestions.append("建立会员等级体系，培养高频充值用户")

            if most_active_frequency == "1次":
                operation_suggestions.append("优化首次充值体验，提升用户留存率")

            operation_suggestions.append("基于充值频次进行用户分层运营")

            # 随机选择分析角度
            analysis_templates = [
                f"充值频次分布显示{random.choice(frequency_insights)}。{random.choice(operation_suggestions)}，同时{random.choice(['加强用户分层管理', '优化充值激励机制', '完善会员权益体系'])}，提升用户生命周期价值。",

                f"从用户充值行为看，{random.choice(frequency_insights)}。{random.choice(operation_suggestions)}，建议{random.choice(['重点关注高价值用户', '优化用户体验流程', '加强精准营销'])}，实现用户价值最大化。",

                f"充值频次数据反映{random.choice(frequency_insights)}。{random.choice(operation_suggestions)}，通过{random.choice(['数据驱动运营', '个性化服务', '会员权益优化'])}，提升用户忠诚度和复购率。"
            ]

            return random.choice(analysis_templates)

        except Exception as e:
            return f"充值频次分布分析生成失败: {str(e)}"

    @staticmethod
    def get_frequency_fallback_analysis() -> str:
        """
        获取充值频次分析备用文本

        Returns:
            str: 备用分析文本
        """
        fallback_options = [
            "充值频次分布数据显示用户充值习惯存在明显差异，建议根据用户充值频次进行分层运营，通过个性化激励和精准营销提升用户粘性，同时关注单次充值用户的转化，实现用户生命周期价值的持续增长。",

            "从充值频次分布情况来看，用户忠诚度和复购意愿呈现分化趋势，建议重点培养高频充值用户，适度激励中频用户，针对单次充值用户制定专门的留存策略，通过数据驱动的运营方式提升整体用户价值。",

            "充值频次分布反映了用户的消费习惯和品牌忠诚度，建议基于频次数据建立用户分层体系，加强对高价值用户的维护，同时通过优化充值体验和会员权益，引导更多用户向高频充值转化，实现用户价值最大化。"
        ]

        return random.choice(fallback_options)
