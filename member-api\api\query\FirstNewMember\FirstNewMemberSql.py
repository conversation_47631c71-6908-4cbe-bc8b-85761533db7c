"""
新会员相关数据SQL查询模块
提供新会员消费人数和储值人数的查询功能
使用welife_hydb数据库进行查询
"""

from typing import Optional
import logging
from core.database import db

logger = logging.getLogger(__name__)

class FirstNewMemberSqlQueries:
    """新会员数据SQL查询类"""

    @staticmethod
    async def get_new_member_visit_count(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> int:
        """获取新会员消费人数统计

        数据库：welife_hydb
        表：dprpt_mobile_users_report
        字段：uconsumeNum（到店新增消费会员数）
        计算方式：SUM(uconsumeNum)

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选，支持多个门店用逗号分隔)

        Returns:
            新会员消费人数
        """
        try:
            # 将日期格式从 YYYY-MM-DD 转换为 YYYYMMDD
            start_date_formatted = start_date.replace('-', '')
            end_date_formatted = end_date.replace('-', '')

            # 处理门店ID条件
            if sid:
                sid_list = [s.strip() for s in sid.split(',')]
                sid_condition = f"AND sid IN ({','.join(sid_list)})"
            else:
                sid_condition = ""

            sql = f"""
            SELECT
                -- 到店新增消费会员数
                SUM(uconsumeNum) AS new_member_visit_count
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = {bid}
              AND ftime BETWEEN {start_date_formatted} AND {end_date_formatted}
              {sid_condition}
            """

            logger.info(f"执行新会员消费人数查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"新会员消费人数查询SQL: {sql}")

            result = await db.execute_welife_hydb_one(sql)
            count = result.get('new_member_visit_count', 0) if result else 0
            # 处理None值
            count = int(count) if count is not None else 0

            logger.info(f"新会员消费人数查询完成，结果: {count}")
            return count

        except Exception as e:
            logger.error(f"获取新会员消费人数失败: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_new_member_charge_count(start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> int:
        """获取新会员储值人数统计

        数据库：welife_hydb
        表：dprpt_mobile_users_report
        字段：uchargeNum（新增储值会员数）
        计算方式：SUM(uchargeNum)

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选，支持多个门店用逗号分隔)

        Returns:
            新会员储值人数
        """
        try:
            # 将日期格式从 YYYY-MM-DD 转换为 YYYYMMDD
            start_date_formatted = start_date.replace('-', '')
            end_date_formatted = end_date.replace('-', '')

            # 处理门店ID条件
            if sid:
                sid_list = [s.strip() for s in sid.split(',')]
                sid_condition = f"AND sid IN ({','.join(sid_list)})"
            else:
                sid_condition = ""

            sql = f"""
            SELECT
                -- 新增储值会员数
                SUM(uchargeNum) AS new_member_charge_count
            FROM welife_hydb.dprpt_mobile_users_report
            WHERE bid = {bid}
              AND ftime BETWEEN {start_date_formatted} AND {end_date_formatted}
              {sid_condition}
            """

            logger.info(f"执行新会员储值人数查询 - bid: {bid}, sid: {sid}, 时间范围: {start_date}-{end_date}")
            logger.debug(f"新会员储值人数查询SQL: {sql}")

            result = await db.execute_welife_hydb_one(sql)
            count = result.get('new_member_charge_count', 0) if result else 0
            # 处理None值
            count = int(count) if count is not None else 0

            logger.info(f"新会员储值人数查询完成，结果: {count}")
            return count

        except Exception as e:
            logger.error(f"获取新会员储值人数失败: {str(e)}", exc_info=True)
            raise