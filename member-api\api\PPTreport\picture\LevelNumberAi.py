# -*- coding: utf-8 -*-
"""
会员等级人数分析AI分析器
提供会员等级人数数据的智能分析功能
"""

import logging
from typing import Dict, Any, List

from services.llm_service import LLMService

logger = logging.getLogger(__name__)

class LevelNumberAiAnalyzer:
    """会员等级人数分析AI分析器"""

    def __init__(self):
        """初始化会员等级人数分析AI分析器"""
        self.llm_service = LLMService()
        logger.info("会员等级人数分析AI分析器初始化完成")

    async def analyze_level_number_data(self, level_data: List[Dict[str, Any]], history_date: str = None, final_date: str = None) -> str:
        """
        分析会员等级人数数据

        Args:
            level_data: 会员等级人数数据列表
            history_date: 历史日期 (YYYY-MM-DD格式)
            final_date: 期末日期 (YYYY-MM-DD格式)

        Returns:
            str: AI分析结果（200-250字的整段分析）
        """
        try:
            if not level_data:
                return "会员等级人数数据不足，无法进行有效分析。建议完善数据收集机制，确保各等级会员人数数据的完整性和准确性，为会员结构优化和精准营销策略制定提供可靠的数据支撑。"

            # 构建时间范围描述
            time_range_desc = ""
            if history_date and final_date:
                time_range_desc = f"对比分析时间：历史基准日期 {history_date} 与期末统计日期 {final_date}"
            elif history_date:
                time_range_desc = f"历史基准日期：{history_date}"
            elif final_date:
                time_range_desc = f"期末统计日期：{final_date}"
            else:
                time_range_desc = "分析时间范围：未指定具体日期"

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的会员运营数据分析师，请基于以下会员等级人数对比数据进行深入分析：

{time_range_desc}

数据概况：
{self._format_level_number_data_summary(level_data, history_date, final_date)}

分析要求：
1. 使用"1、2、3、4、"作为项目符号，按点列出3-4个关键洞察
2. 每个洞察控制在38字以内（标点符号不计入字数），突出关键数据和核心趋势
3. 重点关注：同比增长率、最新月份表现、关键指标变化、渠道效果对比
4. 必须标注具体月份、数值和变化百分比
5. 每个洞察格式：关键发现 + 核心数据 + 趋势判断
6. 每个洞察点必须包含适当的标点符号（逗号、句号等）
7. 禁止空行和格式化符号
8. 进行深入的动态分析：分析历史会员数与期末会员数的对比变化，识别增长或下降趋势

请基于实际数据直接生成专业对比洞察（每点38字以内，含标点符号）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                result = analysis_result.strip()
                logger.info(f"会员等级人数数据AI分析完成")
                return result
            else:
                logger.warning("AI分析返回空结果")
                return "会员等级人数数据分析：AI服务暂时不可用，请稍后重试。"

        except Exception as e:
            logger.error(f"会员等级人数数据AI分析失败: {str(e)}")
            return f"会员等级人数数据分析失败：{str(e)}。建议检查数据格式和AI服务状态。"

    def _format_level_number_data_summary(self, level_data: List[Dict[str, Any]], history_date: str = None, final_date: str = None) -> str:
        """
        格式化会员等级人数数据摘要

        Args:
            level_data: 会员等级人数数据列表
            history_date: 历史日期
            final_date: 期末日期

        Returns:
            str: 格式化的数据摘要
        """
        if not level_data:
            return "无数据"

        try:
            # 提取数据
            level_names = [item.get('ccName', '未知等级') for item in level_data]
            history_counts = [item.get('history_member_count', 0) for item in level_data]
            final_counts = [item.get('final_member_count', 0) for item in level_data]

            # 计算统计指标
            total_levels = len(level_data)
            total_history = sum(history_counts)
            total_final = sum(final_counts)
            total_change = total_final - total_history
            change_rate = (total_change / total_history * 100) if total_history > 0 else 0

            # 计算各等级变化
            level_changes = []
            for i in range(len(level_data)):
                history = history_counts[i]
                final = final_counts[i]
                change = final - history
                change_rate_level = (change / history * 100) if history > 0 else 0
                level_changes.append({
                    'name': level_names[i],
                    'history': history,
                    'final': final,
                    'change': change,
                    'change_rate': change_rate_level
                })

            # 找出变化最大的等级
            max_increase_level = max(level_changes, key=lambda x: x['change']) if level_changes else None
            max_decrease_level = min(level_changes, key=lambda x: x['change']) if level_changes else None
            max_growth_rate_level = max(level_changes, key=lambda x: x['change_rate']) if level_changes else None

            # 构建时间范围信息
            time_info = ""
            if history_date and final_date:
                time_info = f"对比时间：{history_date} 至 {final_date}\n"
            elif history_date:
                time_info = f"历史基准：{history_date}\n"
            elif final_date:
                time_info = f"期末统计：{final_date}\n"

            summary = f"""
{time_info}等级数量：{total_levels}个会员等级
等级名称：{', '.join(level_names)}
历史会员总数：{total_history}人
期末会员总数：{total_final}人
总体变化：{total_change:+d}人，变化率{change_rate:+.2f}%
最大增长等级：{max_increase_level['name'] if max_increase_level else '无'}({max_increase_level['change']:+d}人)
最大减少等级：{max_decrease_level['name'] if max_decrease_level else '无'}({max_decrease_level['change']:+d}人)
最高增长率等级：{max_growth_rate_level['name'] if max_growth_rate_level else '无'}({max_growth_rate_level['change_rate']:+.2f}%)
详细变化：{[f"{item['name']}({item['history']}→{item['final']},{item['change']:+d}人,{item['change_rate']:+.1f}%)" for item in level_changes]}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化会员等级人数数据摘要失败: {str(e)}")
            return f"数据格式化失败: {str(e)}"

    def _get_fallback_analysis(self, level_data: List[Dict[str, Any]], history_date: str = None, final_date: str = None) -> str:
        """
        获取备用分析结果

        Args:
            level_data: 会员等级人数数据列表
            history_date: 历史日期
            final_date: 期末日期

        Returns:
            str: 备用分析结果
        """
        if not level_data:
            return "会员等级人数数据不足，无法进行有效分析。建议完善数据收集机制，确保各等级会员人数数据的完整性和准确性。"

        try:
            # 简单的数据分析
            level_count = len(level_data)
            history_counts = [item.get('history_member_count', 0) for item in level_data]
            final_counts = [item.get('final_member_count', 0) for item in level_data]

            total_history = sum(history_counts)
            total_final = sum(final_counts)
            total_change = total_final - total_history
            change_rate = (total_change / total_history * 100) if total_history > 0 else 0

            time_desc = ""
            if history_date and final_date:
                time_desc = f"从{history_date}到{final_date}期间，"

            if total_change > 0:
                trend_desc = f"会员总数增长{total_change}人，增长率{change_rate:.1f}%，显示出良好的会员发展态势"
            elif total_change < 0:
                trend_desc = f"会员总数减少{abs(total_change)}人，下降率{abs(change_rate):.1f}%，需要关注会员流失问题"
            else:
                trend_desc = "会员总数保持稳定，建议进一步分析各等级内部结构变化"

            return f"{time_desc}当前共有{level_count}个会员等级，{trend_desc}。建议针对不同等级会员制定差异化的维护策略，通过精准营销和个性化服务提升会员粘性，同时优化会员等级体系，促进低等级会员向高等级转化，实现会员价值的持续提升。"

        except Exception as e:
            logger.error(f"生成备用分析失败: {str(e)}")
            return "会员等级人数数据分析遇到技术问题，建议检查数据完整性后重新分析。"


# 创建全局分析器实例
level_number_ai_analyzer = LevelNumberAiAnalyzer()