# -*- coding: utf-8 -*-
"""
充值档位分布图片生成模块
生成充值档位分布的柱状图
"""

import datetime
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

# 设置matplotlib后端（在导入pyplot之前）
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt

# 导入智能标签管理器
from ..SmartLabels import add_smart_bar_labels

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class ChargeDistributionPicGenerator:
    """充值档位分布图片生成器"""

    def __init__(self, bid: str, image_manager):
        """
        初始化图片生成器

        Args:
            bid: 品牌ID
            image_manager: 图片管理器实例
        """
        self.bid = bid
        self.image_manager = image_manager

    def _extract_param(self, query_params, key, default=None):
        """
        从查询参数中提取值

        Args:
            query_params: 查询参数（对象或字典）
            key: 参数键
            default: 默认值

        Returns:
            参数值
        """
        if hasattr(query_params, key):
            return getattr(query_params, key)
        elif isinstance(query_params, dict):
            return query_params.get(key, default)
        else:
            return default

    async def generate_charge_distribution_chart(self, query_params) -> Dict[str, str]:
        """
        生成充值档位分布图表

        Args:
            query_params: 查询参数（对象或字典）

        Returns:
            Dict: 包含图片路径的字典
        """
        try:
            logger.info(f"开始生成充值档位分布图表 - bid: {self.bid}")
            logger.info(f"查询参数类型: {type(query_params)}")
            logger.info(f"图片管理器会话目录: {self.image_manager.session_dir}")

            # 提取查询参数
            start_date = self._extract_param(query_params, 'start_date', '2025-06-01')
            end_date = self._extract_param(query_params, 'end_date', '2025-06-30')
            bid = self._extract_param(query_params, 'bid', self.bid)
            sid = self._extract_param(query_params, 'sid', None)

            logger.info(f"查询时间范围: {start_date} 到 {end_date}")
            logger.info(f"品牌ID: {bid}, 门店ID: {sid}")

            # 获取充值档位分布数据
            charge_data = await self._fetch_charge_distribution_data(start_date, end_date, bid, sid)

            # 初始化结果字典
            result = {}

            # 如果数据获取失败，生成错误图片
            if not charge_data:
                logger.warning("充值档位分布数据获取失败，生成错误图片")
                error_path = self._generate_error_image("charge_distribution", "充值档位分布数据获取失败")
                if error_path:
                    result["charge_distribution"] = error_path
            else:
                # 生成正常图表
                chart_path = await self._generate_chart(
                    charge_data,
                    "充值档位分布分析",
                    "charge_distribution"
                )
                if chart_path:
                    result["charge_distribution"] = chart_path

            # 生成AI分析报告（无论数据是否完整都尝试生成分析）
            try:
                from .ChargeDistributionAi import charge_distribution_ai_analyzer

                if charge_data:
                    # 有数据时生成正常分析
                    ai_analysis = await charge_distribution_ai_analyzer.analyze_charge_distribution_data(charge_data, start_date, end_date)
                    result["charge_distribution_analysis_report"] = ai_analysis
                else:
                    # 无数据时生成默认分析
                    result["charge_distribution_analysis_report"] = "充值档位分布数据缺失，无法进行详细分析。建议检查数据收集机制，确保充值数据完整性，可通过其他渠道补充充值档位数据，建立完善的充值数据监控体系。"

                logger.info("充值档位分布AI分析生成完成")
            except Exception as ai_error:
                logger.error(f"生成充值档位分布AI分析失败: {ai_error}")
                result["charge_distribution_analysis_report"] = "AI分析系统暂时不可用，请稍后重试。建议检查AI服务连接状态和配置，可暂时使用人工分析替代AI分析功能，联系技术支持解决AI分析问题。"

            logger.info(f"充值档位分布图表生成完成，共生成 {len(result)} 个结果")
            return result

        except Exception as e:
            logger.error(f"生成充值档位分布图表失败: {e}")
            import traceback
            traceback.print_exc()
            return {}

    def _generate_error_image(self, image_type: str, error_message: str) -> str:
        """
        生成错误提示图片

        Args:
            image_type: 图片类型
            error_message: 错误信息

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建错误图片
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f'数据生成失败\n{error_message}',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=20, color='red', weight='bold',
                   transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"错误图片生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成错误图片失败: {e}")
            return ""

    async def _fetch_charge_distribution_data(self, start_date: str, end_date: str, bid: str, sid: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取充值档位分布数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            bid: 品牌ID
            sid: 门店ID (可选)

        Returns:
            List: 充值档位分布数据列表
        """
        try:
            from api.query.FirstCharge.ChargeDistributionSql import ChargeDistributionQueries

            logger.info(f"开始获取充值档位分布数据: 时间范围 {start_date} 到 {end_date}")

            # 转换时间格式：YYYY-MM-DD -> YYYYMMDD
            start_date_formatted = start_date.replace('-', '')
            end_date_formatted = end_date.replace('-', '')

            logger.info(f"转换后的时间格式: {start_date_formatted} 到 {end_date_formatted}")

            # 获取充值档位分布数据
            raw_data = await ChargeDistributionQueries.get_charge_amount_distribution(
                start_date_formatted, end_date_formatted, bid, sid
            )
            logger.info(f"原始数据获取完成，共 {len(raw_data)} 个档位")

            # 数据单位转换：从分转换为元
            for item in raw_data:
                item['charge_cash'] = item['charge_cash'] / 100  # 从分转换为元

            # 如果超过10个档位，只取TOP10（数据已按充值笔数降序排列）
            if len(raw_data) > 10:
                logger.info(f"数据超过10个档位，截取TOP10，原始数据: {len(raw_data)}个")
                top_10_data = raw_data[:10]
            else:
                top_10_data = raw_data

            logger.info(f"充值档位分布数据处理完成，最终包含 {len(top_10_data)} 个档位")
            return top_10_data

        except Exception as e:
            logger.error(f"获取充值档位分布数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    async def _generate_chart(self, data: List[Dict[str, Any]], title: str, image_type: str) -> str:
        """
        生成充值档位分布柱状图

        Args:
            data: 充值档位分布数据
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            if not data:
                logger.warning("数据为空，生成空数据提示图表")
                return self._generate_empty_data_chart(image_type)

            # 提取数据
            charge_amounts = [f"{item['charge_cash']:.0f}元" for item in data]  # 充值档位
            charge_counts = [item['charge_count'] for item in data]  # 充值笔数

            logger.info(f"准备生成图表，包含 {len(charge_amounts)} 个充值档位")

            # 创建图表
            fig, ax = plt.subplots(figsize=(14, 8))

            # 设置X轴位置
            x_pos = range(len(charge_amounts))

            # 绘制柱状图 - 使用红色
            ax.bar(x_pos, charge_counts, color='#E74C3C', alpha=0.8)

            # 设置坐标轴
            ax.set_xlabel('充值档位', fontsize=12)
            ax.set_ylabel('充值笔数', fontsize=12)

            ax.set_xticks(x_pos)
            ax.set_xticklabels(charge_amounts, rotation=0, ha='center')
            ax.grid(True, alpha=0.3)

            # 在柱状图上添加数值标签（智能避免重叠）
            max_count = max(charge_counts) if charge_counts and max(charge_counts) > 0 else 10
            add_smart_bar_labels(ax, charge_counts, max_count, fontsize=9, decimal_places=0)

            # 添加数据表格
            self._add_data_table(data, ax)

            # 调整布局
            plt.tight_layout()

            # 保存图片
            file_path = self.image_manager.get_image_path(image_type)
            plt.savefig(file_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close(fig)

            logger.info(f"充值档位分布图表生成完成: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"生成充值档位分布图表失败 {image_type}: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def _add_data_table(self, data: List[Dict[str, Any]], ax):
        """
        在图表下方添加数据表格（横向布局，充值档位为横轴）

        Args:
            data: 数据列表
            ax: 坐标轴对象
        """
        try:
            if not data:
                return

            # 准备横向表格数据
            charge_amounts = [f"{item['charge_cash']:.0f}元" for item in data]
            charge_counts = [f"{item['charge_count']}" for item in data]

            # 构建横向表格数据：第一行是充值档位，第二行是充值笔数
            table_data = [
                charge_amounts,  # 第一行：充值档位
                charge_counts,   # 第二行：充值笔数
            ]

            # 行标题（左侧标签）
            row_labels = ['充值档位', '充值笔数(笔)']

            # 创建表格
            table = ax.table(
                cellText=table_data,
                rowLabels=row_labels,
                cellLoc='center',
                loc='bottom',
                bbox=[0, -0.9, 1, 0.6]  # [x, y, width, height] - 与LevelConsumptionPic保持一致
            )

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)  # 增大字体大小提高可读性
            table.scale(1, 1.2)

            # 设置行标题样式（左侧列）
            for i in range(len(row_labels)):
                table[(i, -1)].set_facecolor('#4472C4')
                table[(i, -1)].set_text_props(weight='bold', color='white')

            # 设置数据单元格样式
            for i in range(len(row_labels)):
                for j in range(len(charge_amounts)):
                    if i == 0:  # 充值档位行使用浅蓝色
                        table[(i, j)].set_facecolor('#E7F3FF')
                        table[(i, j)].set_text_props(weight='bold')
                    elif i % 2 == 1:  # 奇数行使用浅灰色
                        table[(i, j)].set_facecolor('#F8F9FA')
                    else:  # 偶数行使用白色
                        table[(i, j)].set_facecolor('white')

            logger.info(f"横向数据表格创建完成，包含 {len(charge_amounts)} 个充值档位的数据")

        except Exception as e:
            logger.error(f"添加数据表格失败: {e}")



    def _generate_empty_data_chart(self, image_type: str) -> str:
        """
        生成空数据提示图表

        Args:
            title: 图表标题
            image_type: 图片类型

        Returns:
            str: 图片保存路径
        """
        try:
            # 创建空数据图表
            fig, ax = plt.subplots(figsize=(14, 8))

            # 显示"暂无数据"提示
            ax.text(0.5, 0.5, '暂无充值档位分布数据\n请检查数据源或调整查询条件',
                   horizontalalignment='center', verticalalignment='center',
                   fontsize=18, color='#666666', weight='bold',
                   transform=ax.transAxes)

            # 隐藏坐标轴
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 保存图片
            save_path = self.image_manager.get_image_path(image_type)
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

            logger.info(f"空数据图表生成完成: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"生成空数据图表失败: {e}")
            return ""


# 工厂函数
def create_charge_distribution_pic_generator(bid: str, image_manager) -> ChargeDistributionPicGenerator:
    """
    创建充值档位分布图片生成器

    Args:
        bid: 品牌ID
        image_manager: 图片管理器实例

    Returns:
        ChargeDistributionPicGenerator: 图片生成器实例
    """
    return ChargeDistributionPicGenerator(bid, image_manager)
