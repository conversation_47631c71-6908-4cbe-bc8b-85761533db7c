# -*- coding: utf-8 -*-
"""
SmartLabels使用示例
展示如何在图片生成模块中使用智能标签功能
"""

# 使用示例1：在图片生成类中导入和使用

"""
# 在文件顶部导入
from .SmartLabels import add_smart_bar_labels, add_smart_line_labels

class YourPicGenerator:
    def _generate_chart(self, data, title, image_type):
        # ... 创建图表代码 ...
        
        # 示例1：为柱状图添加智能标签
        max_value = max(values) if values else 10
        add_smart_bar_labels(
            ax=ax,                    # matplotlib坐标轴对象
            values=values,            # 数值列表
            max_value=max_value,      # 最大值
            fontsize=9,               # 字体大小
            decimal_places=0,         # 小数位数（0表示整数）
            is_percentage=False       # 是否为百分比数据
        )
        
        # 示例2：为多组柱状图添加智能标签
        # 第一组柱状图
        x_pos_1 = [i - bar_width for i in range(len(values1))]
        add_smart_bar_labels(
            ax=ax1,
            values=values1,
            max_value=max_value,
            x_positions=x_pos_1,
            fontsize=9,
            reset_positions=True      # 重置位置记录
        )
        
        # 第二组柱状图
        x_pos_2 = [i + bar_width for i in range(len(values2))]
        add_smart_bar_labels(
            ax=ax1,
            values=values2,
            max_value=max_value,
            x_positions=x_pos_2,
            fontsize=9,
            reset_positions=False     # 不重置，继续累积位置
        )
        
        # 示例3：为折线图添加智能标签
        add_smart_line_labels(
            ax=ax2,                   # 右Y轴
            values=line_values,       # 折线数据
            max_value=max_line_value,
            fontsize=10,
            color='#000000',          # 字体颜色
            is_percentage=True,       # 百分比数据
            decimal_places=2,         # 保留2位小数
            reset_positions=False     # 与柱状图标签共享位置管理
        )
"""

# 使用示例2：替换现有的标签代码

"""
# 原来的代码：
for i, value in enumerate(values):
    if value > 0:
        ax.text(i, value + max_value * 0.02, f'{value}',
               ha='center', va='bottom', fontsize=9)

# 替换为：
add_smart_bar_labels(ax, values, max_value, fontsize=9, decimal_places=0)
"""

# 使用示例3：处理不同数据类型

"""
# 整数数据（如人数、笔数）
add_smart_bar_labels(ax, counts, max_count, decimal_places=0)

# 金额数据（保留2位小数）
add_smart_bar_labels(ax, amounts, max_amount, decimal_places=2)

# 百分比数据
add_smart_bar_labels(ax, percentages, max_percentage, is_percentage=True, decimal_places=2)

# 折线图百分比数据
add_smart_line_labels(ax, rates, max_rate, color='#F39C12', is_percentage=True, decimal_places=2)
"""

# 使用示例4：复杂图表的完整示例

"""
def _generate_complex_chart(self, data):
    fig, ax1 = plt.subplots(figsize=(14, 8))
    
    # 提取数据
    categories = [item['category'] for item in data]
    values1 = [item['value1'] for item in data]
    values2 = [item['value2'] for item in data]
    percentages = [item['percentage'] for item in data]
    
    # 创建柱状图
    x_pos = range(len(categories))
    bar_width = 0.35
    
    bars1 = ax1.bar([x - bar_width/2 for x in x_pos], values1, 
                   bar_width, label='数值1', color='#4472C4')
    bars2 = ax1.bar([x + bar_width/2 for x in x_pos], values2, 
                   bar_width, label='数值2', color='#E74C3C')
    
    # 创建右Y轴
    ax2 = ax1.twinx()
    line = ax2.plot(x_pos, percentages, 'o-', color='#000000', 
                   linewidth=2, label='百分比')
    
    # 添加智能标签
    max_bar_value = max(max(values1), max(values2))
    max_line_value = max(percentages)
    
    # 柱状图标签
    add_smart_bar_labels(ax1, values1, max_bar_value, 
                        x_positions=[x - bar_width/2 for x in x_pos],
                        fontsize=9, decimal_places=0, reset_positions=True)
    
    add_smart_bar_labels(ax1, values2, max_bar_value,
                        x_positions=[x + bar_width/2 for x in x_pos], 
                        fontsize=9, decimal_places=0, reset_positions=False)
    
    # 折线图标签
    add_smart_line_labels(ax2, percentages, max_line_value,
                         fontsize=10, color='#000000', is_percentage=True,
                         decimal_places=2, reset_positions=False)
    
    # ... 其他图表设置 ...
"""

# 参数说明：

"""
add_smart_bar_labels参数：
- ax: matplotlib坐标轴对象
- values: 数值列表
- max_value: 最大值（用于计算偏移）
- x_positions: X轴位置列表（可选，默认为range(len(values))）
- fontsize: 字体大小（默认9）
- min_spacing: 最小间距比例（默认0.05）
- is_percentage: 是否为百分比数据（默认False）
- decimal_places: 小数位数（默认0）
- reset_positions: 是否重置位置记录（默认True）

add_smart_line_labels参数：
- ax: matplotlib坐标轴对象
- values: 数值列表
- max_value: 最大值（用于计算偏移）
- x_positions: X轴位置列表（可选，默认为range(len(values))）
- fontsize: 字体大小（默认10）
- color: 字体颜色（默认'#000000'）
- min_spacing: 最小间距比例（默认0.05）
- is_percentage: 是否为百分比数据（默认False）
- decimal_places: 小数位数（默认2）
- reset_positions: 是否重置位置记录（默认False）
"""

# 迁移指南：

"""
1. 在文件顶部添加导入：
   from .SmartLabels import add_smart_bar_labels, add_smart_line_labels

2. 找到现有的ax.text()调用，替换为智能标签函数

3. 删除原有的标签添加循环和位置计算代码

4. 删除类中的_add_smart_labels等自定义方法

5. 测试确保标签显示正确且不重叠
"""
