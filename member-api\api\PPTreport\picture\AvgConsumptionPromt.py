# -*- coding: utf-8 -*-
"""
会员平均消费数据AI分析提示词模板
针对会员平均消费数据（会员现金消费笔数、会员储值消费笔数、会员单均消费、会员人均贡献）提供专业的AI分析模板
"""

from typing import Dict, Any, List
import random

class AvgConsumptionAnalysisPrompts:
    """会员平均消费数据AI分析提示词类"""

    # 平均消费数据分析模板句式（去年数据）
    AVG_CONSUMPTION_LAST_YEAR_TEMPLATES = [
        "去年全年平均消费数据显示，会员现金消费笔数月均{avg_cash_consume_count:.0f}笔，会员储值消费笔数月均{avg_prepay_consume_count:.0f}笔，{count_analysis}。",
        "去年会员单均消费表现为月均{avg_consume_amount:.2f}元，会员人均贡献月均{avg_contribution:.2f}元，{consumption_efficiency_analysis}。",
        "去年消费结构呈现{consumption_pattern}特征，{seasonal_analysis}，{consumption_stability_analysis}。",
        "去年消费质量{quality_analysis}，客单价控制{unit_price_analysis}，为今年发展{baseline_significance}。",
        "去年会员消费行为{behavior_analysis}，消费频次表现{consume_frequency_analysis}。"
    ]

    # 平均消费数据分析模板句式（今年数据，包含对比）
    AVG_CONSUMPTION_THIS_YEAR_TEMPLATES = [
        "今年截至目前，会员现金消费笔数月均{avg_cash_consume_count:.0f}笔，较去年同期{cash_comparison}，会员储值消费笔数月均{avg_prepay_consume_count:.0f}笔，{prepay_comparison}。",
        "今年会员单均消费{avg_consume_amount:.2f}元，同比{consume_amount_trend}，会员人均贡献{avg_contribution:.2f}元，{contribution_comparison}。",
        "今年消费结构{structure_comparison}，{monthly_performance}，{consumption_quality_analysis}。",
        "对比去年同期，今年{year_over_year_analysis}，{consumption_optimization}。",
        "今年消费发展{development_assessment}，{future_projection}。"
    ]

    # 消费趋势分析词汇
    CONSUMPTION_TREND_TERMS = {
        "positive": ["呈现稳步上升趋势", "表现出良好增长态势", "显示积极发展势头", "展现强劲增长动力"],
        "negative": ["出现下降趋势", "表现相对疲软", "增长动力不足", "面临增长挑战"],
        "stable": ["保持相对稳定", "波动较小", "增长平稳", "发展稳健"],
        "volatile": ["波动较大", "起伏明显", "变化频繁", "不够稳定"]
    }

    # 消费质量评估词汇
    CONSUMPTION_QUALITY_TERMS = [
        "消费质量优秀，客单价控制良好",
        "消费质量稳健，有提升空间",
        "消费质量一般，需要改进",
        "消费质量偏低，需要重点关注"
    ]

    # 消费行为分析词汇
    CONSUMPTION_BEHAVIOR_TERMS = [
        "现金消费笔数增长良好，储值消费笔数表现优秀",
        "现金消费笔数稳定增长，储值消费笔数需要激励",
        "现金消费笔数增长缓慢，储值消费笔数表现一般",
        "现金消费笔数和储值消费笔数都需要优化"
    ]

    @staticmethod
    def get_avg_consumption_last_year_analysis_prompt(monthly_data: List[Dict[str, Any]]) -> str:
        """
        生成去年会员平均消费数据分析的按点格式提示词（洞见性+实操性）

        Args:
            monthly_data: 去年月度平均消费数据列表

        Returns:
            str: 按点格式的分析提示词
        """
        if not monthly_data:
            return "• 数据不足：去年会员平均消费数据缺失，建议完善数据收集机制"

        # 计算关键指标
        cash_consume_counts = [item.get('cash_consume_count', 0) for item in monthly_data]
        prepay_consume_counts = [item.get('prepay_consume_count', 0) for item in monthly_data]
        avg_consume_amounts = [item.get('avg_consume_amount', 0) for item in monthly_data]
        avg_contributions = [item.get('avg_contribution', 0) for item in monthly_data]

        # 基础统计
        avg_cash_consume_count = sum(cash_consume_counts) / len(cash_consume_counts) if cash_consume_counts else 0
        avg_prepay_consume_count = sum(prepay_consume_counts) / len(prepay_consume_counts) if prepay_consume_counts else 0
        avg_consume_amount = sum(avg_consume_amounts) / len(avg_consume_amounts) if avg_consume_amounts else 0
        avg_contribution = sum(avg_contributions) / len(avg_contributions) if avg_contributions else 0

        # 找出峰值和低谷
        max_consume_idx = avg_consume_amounts.index(max(avg_consume_amounts))
        min_consume_idx = avg_consume_amounts.index(min(avg_consume_amounts))
        peak_month = monthly_data[max_consume_idx]['month']
        valley_month = monthly_data[min_consume_idx]['month']
        peak_value = max(avg_consume_amounts)
        valley_value = min(avg_consume_amounts)

        # 计算波动性
        variance = sum((x - avg_consume_amount) ** 2 for x in avg_consume_amounts) / len(avg_consume_amounts) if avg_consume_amounts else 0
        volatility = (variance ** 0.5) / avg_consume_amount * 100 if avg_consume_amount > 0 else 0

        # 生成按点分析
        analysis_points = []

        # 1. 现金消费笔数洞察 + 实操建议
        if avg_cash_consume_count > 1000:
            analysis_points.append(f"• 现金消费笔数表现优秀：月均{avg_cash_consume_count:.0f}笔，现金支付活跃度高，建议保持现有支付策略并优化现金支付体验")
        elif avg_cash_consume_count > 500:
            analysis_points.append(f"• 现金消费笔数表现稳健：月均{avg_cash_consume_count:.0f}笔，有提升空间，建议推出现金支付优惠并简化支付流程")
        else:
            analysis_points.append(f"• 现金消费笔数偏低：月均{avg_cash_consume_count:.0f}笔，需重点关注，建议降低现金支付门槛并加强现金支付引导")

        # 2. 储值消费笔数洞察 + 实操建议
        prepay_ratio = (avg_prepay_consume_count / (avg_cash_consume_count + avg_prepay_consume_count)) * 100 if (avg_cash_consume_count + avg_prepay_consume_count) > 0 else 0
        if prepay_ratio > 60:
            analysis_points.append(f"• 储值消费笔数卓越：月均{avg_prepay_consume_count:.0f}笔，占比{prepay_ratio:.1f}%，储值活跃度高，建议推出储值增值服务并优化储值体验")
        elif prepay_ratio > 30:
            analysis_points.append(f"• 储值消费笔数良好：月均{avg_prepay_consume_count:.0f}笔，占比{prepay_ratio:.1f}%，建议加强储值营销并提升储值使用频次")
        else:
            analysis_points.append(f"• 储值消费笔数需要改善：月均{avg_prepay_consume_count:.0f}笔，占比{prepay_ratio:.1f}%，建议重新设计储值激励机制并优化储值产品")

        # 3. 单均消费洞察 + 实操建议
        if avg_consume_amount > 300:
            analysis_points.append(f"• 单均消费优异：月均{avg_consume_amount:.2f}元，客单价控制良好，建议推出高价值服务并建立VIP会员体系")
        elif avg_consume_amount > 150:
            analysis_points.append(f"• 单均消费稳健：月均{avg_consume_amount:.2f}元，有提升潜力，建议通过套餐组合和增值服务提升客单价")
        else:
            analysis_points.append(f"• 单均消费偏低：月均{avg_consume_amount:.2f}元，需要改进，建议优化产品定价策略并推出消费激励活动")

        # 4. 人均贡献洞察 + 实操建议
        contribution_efficiency = (avg_contribution / avg_consume_amount * 100) if avg_consume_amount > 0 else 0
        if contribution_efficiency > 80:
            analysis_points.append(f"• 贡献效率优秀：人均贡献{avg_contribution:.2f}元，转化效率{contribution_efficiency:.2f}%，建议扩大成功模式并加强精准营销")
        else:
            analysis_points.append(f"• 贡献效率待优化：人均贡献{avg_contribution:.2f}元，转化效率{contribution_efficiency:.2f}%，建议优化会员运营策略并提升价值转化")

        return "\n".join(analysis_points)

    @staticmethod
    def get_avg_consumption_this_year_analysis_prompt(
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> str:
        """
        生成今年会员平均消费数据分析的按点格式提示词（对比分析+实操性）

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年同期数据列表

        Returns:
            str: 按点格式的对比分析提示词
        """
        if not this_year_data:
            return "• 数据不足：今年会员平均消费数据缺失，建议立即启动数据收集和分析机制"

        # 今年数据统计
        this_cash_consume_counts = [item.get('cash_consume_count', 0) for item in this_year_data]
        this_prepay_consume_counts = [item.get('prepay_consume_count', 0) for item in this_year_data]
        this_avg_consume_amounts = [item.get('avg_consume_amount', 0) for item in this_year_data]
        this_avg_contributions = [item.get('avg_contribution', 0) for item in this_year_data]

        this_avg_cash_consume_count = sum(this_cash_consume_counts) / len(this_cash_consume_counts) if this_cash_consume_counts else 0
        this_avg_prepay_consume_count = sum(this_prepay_consume_counts) / len(this_prepay_consume_counts) if this_prepay_consume_counts else 0
        this_avg_consume_amount = sum(this_avg_consume_amounts) / len(this_avg_consume_amounts) if this_avg_consume_amounts else 0
        this_avg_contribution = sum(this_avg_contributions) / len(this_avg_contributions) if this_avg_contributions else 0

        # 对比分析（如果有去年数据）
        cash_growth = 0
        prepay_growth = 0
        consume_amount_growth = 0
        contribution_growth = 0

        if last_year_data:
            # 取去年同期数据（相同月份数量）
            last_year_same_period = last_year_data[:len(this_year_data)]

            last_cash_consume_counts = [item.get('cash_consume_count', 0) for item in last_year_same_period]
            last_prepay_consume_counts = [item.get('prepay_consume_count', 0) for item in last_year_same_period]
            last_avg_consume_amounts = [item.get('avg_consume_amount', 0) for item in last_year_same_period]
            last_avg_contributions = [item.get('avg_contribution', 0) for item in last_year_same_period]

            last_avg_cash_consume_count = sum(last_cash_consume_counts) / len(last_cash_consume_counts) if last_cash_consume_counts else 0
            last_avg_prepay_consume_count = sum(last_prepay_consume_counts) / len(last_prepay_consume_counts) if last_prepay_consume_counts else 0
            last_avg_consume_amount = sum(last_avg_consume_amounts) / len(last_avg_consume_amounts) if last_avg_consume_amounts else 0
            last_avg_contribution = sum(last_avg_contributions) / len(last_avg_contributions) if last_avg_contributions else 0

            # 计算增长率
            cash_growth = ((this_avg_cash_consume_count - last_avg_cash_consume_count) / last_avg_cash_consume_count * 100) if last_avg_cash_consume_count > 0 else 0
            prepay_growth = ((this_avg_prepay_consume_count - last_avg_prepay_consume_count) / last_avg_prepay_consume_count * 100) if last_avg_prepay_consume_count > 0 else 0
            consume_amount_growth = ((this_avg_consume_amount - last_avg_consume_amount) / last_avg_consume_amount * 100) if last_avg_consume_amount > 0 else 0
            contribution_growth = ((this_avg_contribution - last_avg_contribution) / last_avg_contribution * 100) if last_avg_contribution > 0 else 0

        # 生成按点分析
        analysis_points = []

        # 1. 现金消费笔数对比洞察 + 实操建议
        if cash_growth > 15:
            analysis_points.append(f"• 现金消费笔数增长卓越：较去年同期增长{cash_growth:.2f}%，月均{this_avg_cash_consume_count:.0f}笔，建议加大现金支付优惠力度并扩展现金支付场景")
        elif cash_growth > 0:
            analysis_points.append(f"• 现金消费笔数稳步增长：较去年同期增长{cash_growth:.2f}%，月均{this_avg_cash_consume_count:.0f}笔，建议优化现金支付流程并提升支付体验")
        elif cash_growth > -10:
            analysis_points.append(f"• 现金消费笔数略有下降：较去年同期下降{abs(cash_growth):.2f}%，月均{this_avg_cash_consume_count:.0f}笔，建议调整现金支付策略并优化支付引导")
        else:
            analysis_points.append(f"• 现金消费笔数下降明显：较去年同期下降{abs(cash_growth):.2f}%，月均{this_avg_cash_consume_count:.0f}笔，建议重新评估现金支付策略并加强支付转化")

        # 2. 储值消费笔数对比洞察 + 实操建议
        if prepay_growth > 20:
            analysis_points.append(f"• 储值消费笔数表现突出：较去年同期增长{prepay_growth:.2f}%，月均{this_avg_prepay_consume_count:.0f}笔，建议建立储值忠诚度计划并推出储值奖励机制")
        elif prepay_growth > 0:
            analysis_points.append(f"• 储值消费笔数持续改善：较去年同期增长{prepay_growth:.2f}%，月均{this_avg_prepay_consume_count:.0f}笔，建议加强储值运营并提升储值使用频次")
        else:
            analysis_points.append(f"• 储值消费笔数需要关注：较去年同期变化{prepay_growth:.2f}%，月均{this_avg_prepay_consume_count:.0f}笔，建议重新设计储值激励策略并优化储值产品体验")

        # 3. 单均消费对比洞察 + 实操建议
        if consume_amount_growth > 10:
            analysis_points.append(f"• 单均消费提升显著：较去年同期增长{consume_amount_growth:.2f}%，月均{this_avg_consume_amount:.2f}元，建议推出高价值服务并建立差异化定价体系")
        elif consume_amount_growth > 0:
            analysis_points.append(f"• 单均消费稳步提升：较去年同期增长{consume_amount_growth:.2f}%，月均{this_avg_consume_amount:.2f}元，建议通过套餐组合和增值服务进一步提升客单价")
        else:
            analysis_points.append(f"• 单均消费需要改善：较去年同期变化{consume_amount_growth:.2f}%，月均{this_avg_consume_amount:.2f}元，建议优化产品定价策略并推出消费激励活动")

        # 4. 人均贡献对比洞察 + 实操建议
        if contribution_growth > 15:
            analysis_points.append(f"• 贡献效率大幅提升：较去年同期增长{contribution_growth:.2f}%，月均{this_avg_contribution:.2f}元，建议扩大成功模式并加强精准营销投入")
        elif contribution_growth > 0:
            analysis_points.append(f"• 贡献效率持续优化：较去年同期增长{contribution_growth:.2f}%，月均{this_avg_contribution:.2f}元，建议深化会员运营策略并提升价值转化率")
        else:
            analysis_points.append(f"• 贡献效率待提升：较去年同期变化{contribution_growth:.2f}%，月均{this_avg_contribution:.2f}元，建议重新评估会员价值策略并优化运营效率")

        return "\n".join(analysis_points)